[General]
AppName = "ros-api-go"
Version = "v10.0.2"
Debug = true
PprofAddr = "" # Pprof monitor address, "localhost:6060"
DisableSwagger = false
DisablePrintConfig = false
SiteUrl = "https://ros-api-v2.d.almas.biz"
WebsocketRoute = "/v2/ws"


[General.HTTP]
Addr = ":8040"
ShutdownTimeout = 10
ReadTimeout = 60
WriteTimeout = 60
IdleTimeout = 10
CertFile = ""
KeyFile = ""
TrustedProxies = []  # ["127.0.0.1", "***********"]

[Storage]

[Storage.Cache]
Type = "redis" # memory/badger/redis
Delimiter = ":"

[Storage.Cache.Memory]
CleanupInterval = 60

[Storage.Cache.Badger]
Path = "data/cache"

[Storage.Cache.Redis]
Addr = "*************:6379"
Username = ""
Password = "redis123456."
DB = 0

[Storage.DB]
Debug = true
Type = "mysql" # sqlite3/mysql/postgres
# SQLite3 DSN
# DSN = "data/rosapigo.db"
# MySQL DSN
DSN = "root:AlmasTest2017.@tcp(*************:3306)/ros?charset=utf8mb4&parseTime=True&loc=Local"
# PostgreSQL DSN
# DSN = "host=db user=postgres password=123456 dbname=rosapigo port=5432 sslmode=disable TimeZone=Asia/Shanghai"
MaxLifetime = 86400
MaxIdleTime = 3600
MaxOpenConns = 100
MaxIdleConns = 50
TablePrefix = ""
AutoMigrate = false

[[Storage.DB.Resolver]]
DBType = "mysql"
Sources = ["root:AlmasTest2017.@tcp(*************:3306)/ros?charset=utf8mb4&parseTime=True&loc=Local"]
Replicas = ["root:AlmasTest2017.@tcp(*************:3306)/ros?charset=utf8mb4&parseTime=True&loc=Local"]
Tables = []

[Storage.Redis]
Addr = "*************:6379"
Username = ""
Password = "redis123456."
DB = 0

## 阿里云OSS配置
[Storage.OSS]
AccessKey=""
SecretKey=""
Endpoint="https://ros-files.mulazim.com"
Bucket="ros-files"
IsCNAME=true


[Util]

[Util.Captcha]
Length = 4
Width = 400
Height = 160
CacheType = "memory" # memory/redis

[Util.Captcha.Redis]
Addr = "" # If empty, then use the same configuration as Storage.Cache.Redis
Username = ""
Password = ""
DB = 1
KeyPrefix = "captcha:"

[Util.Prometheus]
Enable = false
Port = 9100
BasicUsername = "admin"
BasicPassword = "admin"
LogApis = [] # Log APIs, e.g. ["/api/v1/users"]
LogMethods = [] # Log HTTP methods, e.g. ["GET"]
DefaultCollect = true

[Dictionary]
UserCacheExp = 4 # hours

## 支付通用配置
[PaymentConfig]
OnlinePayUrl = "https://ros-pay.d.almas.biz/pay/"
PaymentExpires = 300 # seconds

[MQTT]
Host = "mqtt://ros-api-v2.d.almas.biz:1883"
LocalHost = "mqtt://*************:1883"
ClientId = "ros_dev"
Username = "ros_dev"
Password = "ros_dev"
KeepAlive = 60
CleanStartOnInitialConnection = true
## JWT 秘钥
JwtKey = "Almas"
## JWT 有效期 单位： 秒
JwtExp = 3600
RequestTimeOut = 10

[SMS]
AliyunAccessKeyId = "LTAI5tNPBcygs3d2QLYuRgJH"
AliyunAccessKeySecret = "******************************"
