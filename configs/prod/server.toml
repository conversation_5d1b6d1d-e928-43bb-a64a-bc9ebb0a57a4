[General]
AppName = "ros-api-go"
Version = "v10.0.2"
Debug = false
PprofAddr = "" # Pprof monitor address, "localhost:6060"
DisableSwagger = false
DisablePrintConfig = false
SiteUrl = "${SITE_URL}"
WebsocketRoute = "/v2/ws"
[General.HTTP]
Addr = ":${HTTP_PORT}"
ShutdownTimeout = 10
ReadTimeout = 60
WriteTimeout = 60
IdleTimeout = 10
CertFile = ""
KeyFile = ""

[Storage]

[Storage.Cache]
Type = "redis" # memory/badger/redis
Delimiter = ":"

[Storage.Cache.Memory]
CleanupInterval = 60

[Storage.Cache.Badger]
Path = "data/cache"

[Storage.Cache.Redis]
Addr = "${REDIS_HOST}:${REDIS_PORT}"
Username = "${REDIS_USER}"
Password = "${REDIS_PASS}"
DB = 0

[Storage.DB]
Debug = false
Type = "mysql" # sqlite3/mysql/postgres
# SQLite3 DSN
# DSN = "data/rosapigo.db"
# MySQL DSN
DSN = "${DB_USER}:${DB_PASS}@tcp(${DB_URL}:${DB_PORT})/${DB_NAME}?charset=utf8mb4&parseTime=True&loc=Local"
# PostgreSQL DSN
# DSN = "host=db user=postgres password=123456 dbname=rosapigo port=5432 sslmode=disable TimeZone=Asia/Shanghai"
MaxLifetime = 86400
MaxIdleTime = 3600
MaxOpenConns = 100
MaxIdleConns = 50
TablePrefix = ""
AutoMigrate = false

[[Storage.DB.Resolver]]
DBType = "mysql"
Sources = ["${DB_USER}:${DB_PASS}@tcp(${DB_URL}:${DB_PORT})/${DB_NAME}?charset=utf8mb4&parseTime=True&loc=Local"]
Replicas = ["${DB_USER}:${DB_PASS}@tcp(${DB_SLAVE}:${DB_PORT})/${DB_NAME}?charset=utf8mb4&parseTime=True&loc=Local"]
Tables = []

[Storage.Redis]
Addr = "${REDIS_HOST}:${REDIS_PORT}"
Username = "${REDIS_USER}"
Password = "${REDIS_PASS}"
DB = 0

## 阿里云OSS配置
[Storage.OSS]
AccessKey=""
SecretKey=""
Endpoint="https://ros-files.mulazim.com"
Bucket="ros-files"
IsCNAME=true


[Util]

[Util.Captcha]
Length = 4
Width = 400
Height = 160
CacheType = "memory" # memory/redis

[Util.Captcha.Redis]
Addr = "" # If empty, then use the same configuration as Storage.Cache.Redis
Username = ""
Password = ""
DB = 1
KeyPrefix = "captcha:"

[Util.Prometheus]
Enable = false
Port = 9100
BasicUsername = "admin"
BasicPassword = "admin"
LogApis = [] # Log APIs, e.g. ["/api/v1/users"]
LogMethods = [] # Log HTTP methods, e.g. ["GET"]
DefaultCollect = true

[Dictionary]
UserCacheExp = 4 # hours

## 支付通用配置
[PaymentConfig]
OnlinePayUrl = "https://ros-pay.mulazim.com/pay"
PaymentExpires = 300 # seconds

[MQTT]
Host = "${MQTT_DSN}"
LocalHost = "${LOCAL_MQTT_DSN}"
ClientId = "${MQTT_CLIENT_ID}"
Username = "${MQTT_USER}"
Password = "${MQTT_PASS}"
KeepAlive = 60
CleanStartOnInitialConnection = true
## JWT 秘钥
JwtKey = "${MQTT_JWT_SECRET}"
## JWT 有效期 单位： 秒
JwtExp = 3600
RequestTimeOut = 10