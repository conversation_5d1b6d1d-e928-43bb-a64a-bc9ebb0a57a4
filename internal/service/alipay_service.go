package service

import (
	"context"
	"encoding/base64"
	"fmt"
	"github.com/go-pay/gopay"
	"github.com/go-pay/gopay/alipay"
	alipayV3 "github.com/go-pay/gopay/alipay/v3"
	"go.uber.org/zap"
	"gorm.io/gorm"
	"net/http"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/alipayx"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"time"
)

type AlipayService struct {
	PaymentClient *alipayx.AlipayClient
	MiniClient    *alipayx.AlipayMiniClient
	DB            *gorm.DB
}

// SystemOauthToken 获取用户授权token
func (serv *AlipayService) SystemOauthToken(ctx context.Context, merchantNo, authCode string) (*alipayV3.SystemOauthTokenRsp, error) {
	// 请求参数
	bm := make(gopay.BodyMap)
	bm.Set("grant_type", "authorization_code").
		Set("code", authCode)

	// 商家授权token
	//bm.Set(alipayV3.HeaderAppAuthToken, merchant.AlipayAppAuthToken)

	token, err := serv.MiniClient.SystemOauthToken(ctx, bm)
	if err != nil {
		logging.Context(ctx).Error("获取商家授权token失败",
			zap.String("merchantNo", merchantNo),
			zap.String("authCode", authCode),
			zap.Any("bm", bm),
			zap.Any("resp", token),
		)
		return nil, errors.BadRequest("", "获取商家授权token失败", err)
	}
	if token.StatusCode != http.StatusOK {
		logging.Context(ctx).Error("获取商家授权token失败",
			zap.String("merchantNo", merchantNo),
			zap.String("authCode", authCode),
			zap.Any("bm", bm),
			zap.Any("resp", token),
		)
		return nil, errors.BadRequest("", "获取商家授权token失败：%v", token.ErrResponse.Message)
	}
	return token, nil
}

// MicroPay 支付宝付款码支付
func (serv *AlipayService) MicroPay(ctx context.Context, authCode string, paymentLog *model.MerchantPaymentLogModel, merchant *model.MerchantModel) (*schema.PaymentResult, error) {

	// 校验支付金额
	if paymentLog.Amount < 1 {
		return nil, errors.BadRequest("", "PayAmountInvalid")
	}

	attach := make(gopay.BodyMap)
	attach.Set("pn", paymentLog.PaymentNo).
		Set("no", paymentLog.OrderNo)

	bm := make(gopay.BodyMap)
	bm.Set("out_trade_no", paymentLog.OutTradeNo).
		Set("subject", paymentLog.Subject).
		Set("total_amount", util.DivideFloat(float64(paymentLog.Amount), 100)).
		Set("auth_code", authCode).
		Set("scene", "bar_code").
		Set("time_expire", paymentLog.ExpiresAt.Format(time.DateTime)).
		Set("store_id", merchant.No)

	// 商家授权token
	bm.Set(alipayV3.HeaderAppAuthToken, merchant.AlipayAppAuthToken)

	logging.Context(ctx).Debug("支付宝付款码支付请求参数",
		zap.String("merchantNo", paymentLog.MerchantNo),
		zap.String("orderNo", paymentLog.OrderNo),
		zap.Int64("orderType", paymentLog.OrderType),
		zap.String("paymentNo", paymentLog.PaymentNo),
		zap.Any("params", bm),
	)

	result, err := serv.PaymentClient.TradePay(ctx, bm)
	if err != nil {
		logging.Context(ctx).Error("支付宝付款码支付请求失败",
			zap.String("merchantNo", paymentLog.MerchantNo),
			zap.String("orderNo", paymentLog.OrderNo),
			zap.Int64("orderType", paymentLog.OrderType),
			zap.String("paymentNo", paymentLog.PaymentNo),
			zap.Any("params", bm.JsonBody()),
			zap.Error(err))
		return nil, errors.BadRequest("", "支付请求失败")
	}

	if result.StatusCode != http.StatusOK {
		if result.ErrResponse.Code != "ACQ.USER_PAYING" {
			logging.Context(ctx).Error("支付宝付款码支付失败",
				zap.String("merchantNo", paymentLog.MerchantNo),
				zap.String("orderNo", paymentLog.OrderNo),
				zap.Int64("orderType", paymentLog.OrderType),
				zap.String("paymentNo", paymentLog.PaymentNo),
				zap.Any("params", bm.JsonBody()),
				zap.Any("result", result))
			return nil, errors.BadRequest("", result.ErrResponse.Message)
		}
	}
	// 有交易单号和交易时间, 视为支付成功
	if result.TradeNo != "" && result.GmtPayment != "" {
		// 如果支付成功直接返回
		paidAt, _ := alipayx.ParseDateTimeV3(result.GmtPayment)
		return &schema.PaymentResult{
			Status:        consts.PAY_STATUS_PAID,
			PaymentTypeID: consts.PAY_TYPE_ALIPAY,
			TransactionID: result.TradeNo,
			OutTradeNo:    result.OutTradeNo,
			OrderNo:       paymentLog.OrderNo,
			PaymentNo:     paymentLog.PaymentNo,
			MerchantNo:    paymentLog.MerchantNo,
			OpenID:        result.BuyerOpenId,
			PaidAt:        &paidAt,
			TradeType:     consts.TRADE_TYPE_MICROPAY,
			TradeState:    "TRADE_SUCCESS",
			TradeDesc:     "支付成功",
		}, nil
	}

	// 轮询查询订单状态
	tryTimes := 0
	var queryResult *schema.PaymentResult

	for tryTimes < 10 {
		logging.Context(ctx).Info("支付宝付款码支付查询订单状态",
			zap.String("merchantNo", paymentLog.MerchantNo),
			zap.String("orderNo", paymentLog.OrderNo),
			zap.Int64("orderType", paymentLog.OrderType),
			zap.String("paymentNo", paymentLog.PaymentNo),
			zap.String("out_trade_no", paymentLog.OutTradeNo),
			zap.Int("tryTimes", tryTimes))
		time.Sleep(time.Second * 4)
		queryResult, err = serv.QueryByOutTradeNo(ctx, paymentLog.OutTradeNo)

		if err != nil {
			logging.Context(ctx).Error("支付宝付款码支付查询订单状态失败",
				zap.String("merchantNo", paymentLog.MerchantNo),
				zap.String("orderNo", paymentLog.OrderNo),
				zap.Int64("orderType", paymentLog.OrderType),
				zap.String("paymentNo", paymentLog.PaymentNo),
				zap.String("out_trade_no", paymentLog.OutTradeNo),
				zap.Error(err))
			return nil, err
		}

		// 如果支付成功
		if queryResult.Status == consts.PAY_STATUS_PAID {
			// 如果支付成功直接返回
			paidAt, _ := alipayx.ParseDateTimeV3(result.GmtPayment)
			return &schema.PaymentResult{
				Status:        consts.PAY_STATUS_PAID,
				PaymentTypeID: consts.PAY_TYPE_ALIPAY,
				TransactionID: result.TradeNo,
				OutTradeNo:    result.OutTradeNo,
				OrderNo:       paymentLog.OrderNo,
				PaymentNo:     paymentLog.PaymentNo,
				MerchantNo:    paymentLog.MerchantNo,
				OpenID:        result.BuyerOpenId,
				PaidAt:        &paidAt,
				TradeType:     consts.TRADE_TYPE_MICROPAY,
				TradeState:    queryResult.TradeState,
				TradeDesc:     "支付成功",
			}, nil
		}
		tryTimes++
	}

	logging.Context(ctx).Error("支付宝付款码支付失败",
		zap.String("merchantNo", paymentLog.MerchantNo),
		zap.String("orderNo", paymentLog.OrderNo),
		zap.Int64("orderType", paymentLog.OrderType),
		zap.String("paymentNo", paymentLog.PaymentNo),
		zap.String("out_trade_no", paymentLog.OutTradeNo),
		zap.Any("params", bm.JsonBody()),
		zap.Any("queryResult", queryResult))

	return nil, errors.BadRequest("1003", "PaymentFailed")

}

// QueryByOutTradeNo 根据outTradeNo查询订单
func (serv *AlipayService) QueryByOutTradeNo(ctx context.Context, outTradeNo string) (*schema.PaymentResult, error) {

	bm := make(gopay.BodyMap)
	bm.Set("out_trade_no", outTradeNo)

	queryResult, err := serv.PaymentClient.TradeQuery(ctx, bm)
	if err != nil {
		return nil, errors.BadRequest("", "支付宝支付结果查询失败", err)
	}

	logging.Context(ctx).Debug("支付宝支付结果查询结果", zap.Any("params", bm), zap.Any("result", queryResult))

	// 解析attach信息
	paymentLog := &model.MerchantPaymentLogModel{}
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("out_trade_no=?", outTradeNo), paymentLog)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.NotFound("140", "PaymentLogNotFound")
	}

	//
	paymentResult := &schema.PaymentResult{
		Status:        consts.PAY_STATUS_UNPAID,
		PaymentTypeID: consts.PAY_TYPE_WECHAT,
		PaymentNo:     paymentLog.PaymentNo,
		MerchantNo:    paymentLog.MerchantNo,
		OrderNo:       paymentLog.OrderNo,
		OutTradeNo:    paymentLog.OutTradeNo,
	}

	if queryResult.StatusCode != http.StatusOK {
		logging.Context(ctx).Error("支付宝支付结果查询失败",
			zap.String("outTradeNo", outTradeNo),
			zap.Any("params", bm),
			zap.Any("queryResponse", queryResult))
		return nil, errors.BadRequest("", queryResult.ErrResponse.Message)
	}
	// 未支付
	if queryResult.TradeStatus == "WAIT_BUYER_PAY" {
		paymentResult.TradeState = queryResult.TradeStatus
		return paymentResult, nil
	}

	// 交易关闭表示支付失败
	if queryResult.TradeStatus == "TRADE_CLOSED" {
		paymentResult.Status = consts.PAY_STATUS_FAILED
		paymentResult.TradeState = queryResult.TradeStatus
		return paymentResult, nil
	}
	// 交易成功 (TRADE_SUCCESS 和 TRADE_FINISHED)
	// 解析时间
	paidAt, err := alipayx.ParseDateTimeV3(queryResult.SendPayDate)

	paymentResult.OpenID = queryResult.BuyerOpenId
	paymentResult.Status = consts.PAY_STATUS_PAID
	paymentResult.TradeState = queryResult.TradeStatus
	paymentResult.TransactionID = queryResult.TradeNo
	paymentResult.PaidAt = &paidAt
	return paymentResult, nil

}

// JsAPIPay 支付宝JSAPI支付
func (serv *AlipayService) JsAPIPay(ctx context.Context, openid string, paymentLog *model.MerchantPaymentLogModel, merchant *model.MerchantModel) (*alipayV3.TradeCreateRsp, error) {

	// 校验支付金额
	if paymentLog.Amount < 1 {
		return nil, errors.BadRequest("", "PayAmountInvalid")
	}
	// 构造请求参数
	attach := fmt.Sprintf("pn=%s&no=%s", paymentLog.PaymentNo, paymentLog.OrderNo)
	bm := make(gopay.BodyMap)
	bm.
		Set("out_trade_no", paymentLog.OutTradeNo).
		Set("total_amount", util.DivideFloat(float64(paymentLog.Amount), 100)).
		Set("subject", paymentLog.Subject).
		Set("body", paymentLog.Subject).
		Set("product_code", "JSAPI_PAY").
		Set("op_app_id", config.C.Alipay.Mini.AppID). // 小程序APPID
		Set("op_buyer_open_id", openid).
		Set("time_expire", paymentLog.ExpiresAt.Format(time.DateTime)).
		Set("passback_params", attach).
		Set("notify_url", alipayx.GetPaymentNotifyUrl(paymentLog.OrderType, paymentLog.PaymentNo))

	bm.Set(alipayV3.HeaderAppAuthToken, merchant.AlipayAppAuthToken)

	logging.Context(ctx).Debug("支付宝JSAPI支付请求参数", zap.Any("params", bm))

	//serv.PaymentClient.AppAuthToken = merchant.AlipayAppAuthToken

	jsapiResponse, err := serv.PaymentClient.TradeCreate(ctx, bm)
	if err != nil {
		return nil, err
	}
	if jsapiResponse.StatusCode != http.StatusOK {
		logging.Context(ctx).Error("Alipay JsAPIPay Failed",
			zap.String("merchantNo", paymentLog.MerchantNo),
			zap.String("paymentNo", paymentLog.PaymentNo),
			zap.String("outTradeNo", paymentLog.OutTradeNo),
			zap.Any("params", bm),
			zap.Any("jsapiResponse", jsapiResponse))

		return nil, errors.BadRequest("", jsapiResponse.ErrResponse.Message)
	}

	return jsapiResponse, nil
}

// ParsePaymentNotify 解析支付宝支付通知
func (serv *AlipayService) ParsePaymentNotify(ctx context.Context, req *http.Request) (*schema.PaymentResult, error) {

	// 解析支付宝回调
	notifyReq, err := alipay.ParseNotifyToBodyMap(req)
	if err != nil {
		return nil, err
	}

	logging.Context(ctx).Info(
		"接受支付宝支付付款通知",
		zap.Any("notifyReq", notifyReq),
	)
	// 支付宝异步通知验签（公钥证书模式）
	alipayPublicCert, _ := base64.StdEncoding.DecodeString(config.C.Alipay.PublicCert)
	ok, err := alipay.VerifySignWithCert(alipayPublicCert, notifyReq)
	if err != nil {
		return nil, errors.BadRequest("", "支付宝验签失败："+err.Error())
	}

	// 解析attach信息
	paymentLog := &model.MerchantPaymentLogModel{}
	ok, err = util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("out_trade_no=?", notifyReq.Get("out_trade_no")), paymentLog)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, errors.NotFound("140", "PaymentLogNotFound")
	}

	paidAt, err := alipayx.ParseDateTimeV3(notifyReq.Get("gmt_payment"))
	if err != nil {
		return nil, err
	}

	// 更新支付日志
	util.GetDB(ctx, serv.DB).Model(paymentLog).Updates(map[string]interface{}{
		"transaction_id": notifyReq.Get("trade_no"),
		"paid_at":        &paidAt,
		"mch_id":         notifyReq.Get("seller_id"),
		"sub_mch_id":     notifyReq.Get("auth_app_id"),
		"trade_state":    notifyReq.Get("trade_status"),
		"trade_desc":     notifyReq.Get("trade_status"),
		"open_id":        notifyReq.Get("buyer_id"),
	})

	paymentResult := schema.PaymentResult{
		OpenID:        notifyReq.Get("buyer_id"),
		MerchantNo:    paymentLog.MerchantNo,
		OrderNo:       paymentLog.OrderNo,   // no: orderNo
		PaymentNo:     paymentLog.PaymentNo, // pn: paymentNo
		PaymentTypeID: consts.PAY_TYPE_ALIPAY,
		OutTradeNo:    paymentLog.OutTradeNo,
		TradeType:     paymentLog.TradeType,
		TradeState:    notifyReq.Get("trade_status"),
		TradeDesc:     notifyReq.Get("trade_status"),
		TransactionID: notifyReq.Get("trade_no"),
		PaidAt:        &paidAt,
	}

	// 如果支付成功
	if notifyReq.Get("trade_status") == "TRADE_SUCCESS" || notifyReq.Get("trade_status") == "TRADE_FINISHED" {
		paymentResult.Status = consts.PAY_STATUS_PAID
	}

	return &paymentResult, nil
}

// RefundByRefundLog 退款
func (serv *AlipayService) RefundByRefundLog(ctx context.Context, refundLog *model.MerchantRefundLogModel) (*schema.RefundResult, error) {
	// 初始化 BodyMap
	bm := make(gopay.BodyMap).
		Set("out_trade_no", refundLog.OutTradeNo).
		Set("out_request_no", refundLog.OutRefundNo).
		Set("refund_reason", "撤销付款").
		Set("refund_amount", util.DivideFloat(float64(refundLog.Amount), 100)).
		Set("notify_url", alipayx.GetRefundNotifyUrl(refundLog.OutRefundNo))

	var merchant model.MerchantModel
	one, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("no=?", refundLog.MerchantNo), &merchant)
	if err != nil || !one {
		return nil, errors.BadRequest("", "MerchantNotFound")
	}

	bm.Set(alipayV3.HeaderAppAuthToken, merchant.AlipayAppAuthToken)

	logging.Context(ctx).Debug("支付宝退款请求参数", zap.Any("params", bm))

	refundRes, err := serv.PaymentClient.TradeRefund(ctx, bm)

	if err != nil {
		return nil, err
	}

	if refundRes.StatusCode != http.StatusOK {

		logging.Context(ctx).Error("支付宝退款失败",
			zap.Any("refund_log_id", refundLog.ID),
			zap.String("merchant_no", refundLog.MerchantNo),
			zap.String("order_no", refundLog.OrderNo),
			zap.Any("params", bm),
			zap.Any("error", refundRes.ErrResponse))
		return nil, errors.BadRequest("", fmt.Sprintf("支付宝退款失败: %v", refundRes.ErrResponse.Message))
	}

	refundResult := &schema.RefundResult{
		Status:        consts.REFUND_STATUS_WAITING,
		PaymentTypeID: refundLog.PaymentTypeID,
		OutTradeNo:    refundLog.OutTradeNo,
		OutRefundNo:   refundLog.OutRefundNo,
	}

	// 如果成功
	if refundRes.FundChange == "Y" {
		refundAt := time.Now()

		refundResult.Status = consts.REFUND_STATUS_SUCCESS
		refundResult.RefundAt = &refundAt
		refundResult.TradeState = "SUCCESS"
		refundResult.TradeDesc = "SUCCESS"
	} else {
		refundResult.Status = consts.REFUND_STATUS_FAILED
		refundResult.TradeState = "FAIL"
		refundResult.TradeDesc = "FAIL"
	}

	return refundResult, nil
}
