package service

import (
	"context"
	"gorm.io/gorm"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"
)

// PaymentLogService 支付记录服务
type PaymentLogService struct {
	DB *gorm.DB
}

// CreatePaymentLog 创建支付记录
func (serv *PaymentLogService) CreatePaymentLog(ctx context.Context, merchant *model.MerchantModel, paymentInfo *model.MerchantPaymentModel, tradeType string, orderType int64, clientIP string) (*model.MerchantPaymentLogModel, error) {
	subject := merchant.NameZh + "-就餐费用"
	prefix := consts.OrderPaymentPrefix
	if orderType == consts.ORDER_TYPE_RECHARGE {
		subject = merchant.NameZh + "-会员充值"
		prefix = consts.VIPPaymentPrefix
	}
	expiredAt := paymentInfo.ExpiresAt
	if tradeType == consts.TRADE_TYPE_MICROPAY {
		now := time.Now().Add(35 * time.Second)
		expiredAt = &now
	}

	paymentLog := &model.MerchantPaymentLogModel{
		MerchantNo:    paymentInfo.MerchantNo,
		OrderNo:       paymentInfo.OrderNo,
		OrderType:     orderType,
		PaymentTypeID: paymentInfo.PaymentTypeID,
		PaymentNo:     paymentInfo.PaymentNo,
		Subject:       subject,
		ClientIP:      clientIP,
		TradeType:     tradeType,
		Amount:        paymentInfo.Amount,
		PayType:       paymentInfo.PayType,
		ExpiresAt:     expiredAt,
		OutTradeNo:    util.SerialNumber(prefix),
	}

	if paymentInfo.PaymentTypeID == consts.PAY_TYPE_WECHAT { // 微信支付
		paymentLog.PayType = "wechat"
		paymentLog.MchID = merchant.MchID
		paymentLog.SubMchID = merchant.SubMchID
	} else if paymentInfo.PaymentTypeID == consts.PAY_TYPE_ALIPAY { //支付宝支付
		paymentLog.PayType = "alipay"
		paymentLog.MchID = merchant.MchID
		paymentLog.SubMchID = merchant.AlipaySubMchID
	}

	err := util.GetDB(ctx, serv.DB).Create(paymentLog).Error
	return paymentLog, err
}

// UpdatePaymentStatus 更新支付状态
func (serv *PaymentLogService) UpdatePaymentStatus(ctx context.Context, paymentResult *schema.PaymentResult) (*model.MerchantPaymentLogModel, error) {
	paymentLog, err := serv.GetByOutTradeNo(ctx, paymentResult.OutTradeNo, paymentResult.MerchantNo)
	if err != nil {
		return nil, err
	} else if paymentLog == nil {
		return nil, errors.NotFound("", "PaymentNotFound")
	}
	// 如果已经支付过了，则不再更新
	if paymentLog.Status == consts.PAY_STATUS_PAID {
		return paymentLog, nil
	}
	// 更新支付状态
	if paymentResult.Status == consts.PAY_STATUS_PAID {
		return paymentLog, util.GetDB(ctx, serv.DB).Model(paymentLog).Updates(map[string]interface{}{
			"open_id":        paymentResult.OpenID,
			"status":         consts.PAY_STATUS_PAID,
			"paid_at":        paymentResult.PaidAt,
			"transaction_id": paymentResult.TransactionID,
			"trade_state":    paymentResult.TradeState,
			"trade_desc":     paymentResult.TradeDesc,
			"trade_type":     paymentResult.TradeType,
		}).Error
	}
	if paymentResult.Status == consts.PAY_STATUS_FAILED {
		return paymentLog, util.GetDB(ctx, serv.DB).Model(paymentLog).Updates(map[string]interface{}{
			"open_id":    paymentResult.OpenID,
			"status":     consts.PAY_STATUS_FAILED,
			"trade_desc": paymentResult.TradeDesc,
		}).Error
	}
	return paymentLog, nil
}

// GetByOutTradeNo 根据商户订单号获取支付记录
func (serv *PaymentLogService) GetByOutTradeNo(ctx context.Context, outTradeNo string, merchantNo string) (*model.MerchantPaymentLogModel, error) {
	paymentLog := &model.MerchantPaymentLogModel{}
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("merchant_no=? AND out_trade_no=?", merchantNo, outTradeNo), paymentLog)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return paymentLog, nil
}

// GetByPaymentNo 根据支付单号获取支付记录
func (serv *PaymentLogService) GetByPaymentNo(ctx context.Context, paymentNo string, merchantNo string) (*model.MerchantPaymentLogModel, error) {
	paymentLog := &model.MerchantPaymentLogModel{}
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("merchant_no=? AND payment_no=?", merchantNo, paymentNo), paymentLog)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return paymentLog, nil
}

// GetPaidRecordByPaymentNo 根据支付单号获取已支付的支付记录
func (serv *PaymentLogService) GetPaidRecordByPaymentNo(ctx context.Context, paymentNo string, merchantNo string) (*model.MerchantPaymentLogModel, error) {
	paymentLog := &model.MerchantPaymentLogModel{}
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("merchant_no=? AND payment_no=? AND status=?", merchantNo, paymentNo, consts.PAY_STATUS_PAID), paymentLog)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return paymentLog, nil
}

// HasPaidRecordExists 判断是否存在已支付记录（相同paymentNo 能够多次支付）
func (serv *PaymentLogService) HasPaidRecordExists(ctx context.Context, paymentNo string, merchantNo string) (*model.MerchantPaymentLogModel, error) {
	paymentLog := &model.MerchantPaymentLogModel{}
	ok, err := util.Exists(ctx, util.GetDB(ctx, serv.DB).Where("merchant_no=? AND payment_no=? AND status=?", merchantNo, paymentNo, consts.PAY_STATUS_PAID))
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return paymentLog, nil
}
