package service

import (
	"context"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

type RechargeOrderService struct {
	DB *gorm.DB
}

// Create 创建充值订单
func (serv *RechargeOrderService) Create(ctx context.Context, order *model.CustomerRechargeOrderModel) error {
	return util.GetDB(ctx, serv.DB).Create(order).Error
}

// GetByID 根据ID获取充值订单
func (serv *RechargeOrderService) GetByID(ctx context.Context, merchantNo string, id int64) (*model.CustomerRechargeOrderModel, error) {
	var order model.CustomerRechargeOrderModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("merchant_no = ? AND id = ?", merchantNo, id), &order)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &order, err
}

// UpdateStatus 更新订单状态
func (serv *RechargeOrderService) UpdateStatus(ctx context.Context, id int64, status int64) error {
	return util.GetDB(ctx, serv.DB).Model(&model.CustomerRechargeOrderModel{}).
		Where("id = ?", id).
		Update("status", status).Error
}

// ListByCustomerID 根据客户ID获取充值订单列表
func (serv *RechargeOrderService) ListByCustomerID(ctx context.Context, merchantNo string, customerID int64) ([]*model.CustomerRechargeOrderModel, error) {
	var orders []*model.CustomerRechargeOrderModel
	err := util.GetDB(ctx, serv.DB).Where("merchant_no = ? AND customer_id = ?", merchantNo, customerID).Find(&orders).Error
	return orders, err
}

// GetByOrderNo 根据订单号获取充值订单
func (serv *RechargeOrderService) GetByOrderNo(ctx context.Context, merchantNo string, orderNo string) (*model.CustomerRechargeOrderModel, error) {
	var order model.CustomerRechargeOrderModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Preload("Customer").
		Where("merchant_no = ? AND no = ?", merchantNo, orderNo), &order)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &order, err
}

// ListForHandoverDetail 根据cashierId获取VIP充值统计(用于交接班)
func (serv *RechargeOrderService) ListForHandoverDetail(ctx context.Context, cashierId int64, merchantNo string,
	startDate time.Time, endDate time.Time) ([]*schema.CashierRechargeStatistics, error) {
	var orders []*schema.CashierRechargeStatistics
	err := util.GetDB(ctx, serv.DB).
		Select("payment_type_id, SUM(recharge_amount) as recharge_amount, SUM(present_amount) as present_amount").
		Model(&model.CustomerRechargeOrderModel{}).
		Where("status = ? AND merchant_no = ? AND cashier_id = ?", model.RechargeOrderStatusSuccess, merchantNo, cashierId).
		Where("created_at BETWEEN ? AND ?", startDate, endDate).
		Group("payment_type_id").
		Find(&orders).Error
	return orders, err
}
