package service

import (
	"context"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"time"

	"go.uber.org/zap"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// CustomerService 客户业务逻辑
type CustomerService struct {
	DB *gorm.DB
}

// GetByID 根据ID获取客户信息
func (serv *CustomerService) GetByID(ctx context.Context, id int64) (*model.CustomerModel, error) {
	var customer model.CustomerModel
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("id = ?", id), &customer)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &customer, err
}

// GetRechargeOrderByPaymentNo 根据充值订单号获取客户信息
func (serv *CustomerService) GetRechargeOrderByPaymentNo(ctx context.Context, paymentNo string) (*model.CustomerRechargeOrderModel, error) {
	// 先查询充值订单
	var order model.CustomerRechargeOrderModel
	_, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).
		Where("no = ?", paymentNo).
		Preload("Customer"), &order)
	if err != nil {
		return nil, err
	}

	return &order, nil
}

// IncreaseBalance 增加余额
func (serv *CustomerService) IncreaseBalance(ctx context.Context, id int64, amount float64) error {

	// 使用SELECT FOR UPDATE锁定记录
	var customer model.CustomerModel
	if err := util.GetDB(ctx, serv.DB).
		Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", id).
		First(&customer).Error; err != nil {
		return err
	}

	// 更新余额
	return util.GetDB(ctx, serv.DB).Model(&model.CustomerModel{}).
		Where("id = ?", id).
		Update("balance", gorm.Expr("balance + ?", amount)).Error
}

// DecreaseBalance 减少余额
func (serv *CustomerService) DecreaseBalance(ctx context.Context, id int64, amount float64) error {

	// 使用SELECT FOR UPDATE锁定记录
	var customer model.CustomerModel
	if err := util.GetDB(ctx, serv.DB).Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("id = ?", id).
		First(&customer).Error; err != nil {
		return err
	}

	// 检查余额是否充足
	if customer.Balance < amount {
		return errors.BadRequest("", "CustomerBalanceNotEnough")
	}

	// 更新余额
	return util.GetDB(ctx, serv.DB).Model(&model.CustomerModel{}).
		Where("id = ?", id).
		Update("balance", gorm.Expr("balance - ?", amount)).Error
}

// Pay 会员支付
func (serv *CustomerService) Pay(
	ctx context.Context,
	customer *model.CustomerModel,
	merchantPayment *model.MerchantPaymentModel,
	cashierId int64) error {
	db := util.GetDB(ctx, serv.DB)
	amount := util.DivideFloat(float64(merchantPayment.Amount), 100)
	balance := util.SubtractFloat(customer.Balance, amount)
	err := db.Transaction(func(tx *gorm.DB) error {
		customerConsumptionLog := model.CustomerConsumptionLogModel{
			MerchantNo:    merchantPayment.MerchantNo,
			CustomerID:    customer.ID,
			Type:          model.ConsumptionTypeConsume,
			OrderID:       merchantPayment.ID,
			Amount:        amount,
			PresentAmount: 0,
			Balance:       balance,
			PaymentTypeID: merchantPayment.PaymentTypeID,
			CashierID:     cashierId,
		}
		db.Create(&customerConsumptionLog)
		db.Model(&model.CustomerModel{}).
			Where("id = ?", customer.ID).
			Update("balance", balance)
		db.Model(&model.MerchantPaymentModel{}).Where("id = ?", merchantPayment.ID).Updates(map[string]interface{}{
			"status":  consts.PAY_STATUS_PAID,
			"paid_at": time.Now(),
		})
		return nil
	})
	if err != nil {
		logging.Context(ctx).Error("会员支付失败",
			zap.Int64("merchant_payment_id", merchantPayment.ID),
			zap.Int64("customer_id", customer.ID),
			zap.Float64("cashier_id", float64(cashierId)),
			zap.Float64("amount", amount),
			zap.String("order_no", merchantPayment.OrderNo),
			zap.String("payment_no", merchantPayment.PaymentNo),
			zap.Error(err))
		db.Model(&model.MerchantPaymentModel{}).
			Where("id = ?", merchantPayment.ID).
			Update("status", consts.PAY_STATUS_FAILED)
	}
	return err
}

func (serv *CustomerService) GetHandoverNewCustomer(ctx context.Context, handoverLog *model.HandoverLogModel, endAt time.Time) (int64, error) {
	db := util.GetDB(ctx, serv.DB)
	var newCustomers int64
	return newCustomers, db.Model(&model.CustomerModel{}).
		Where("cashier_id = ? AND merchant_no = ?", handoverLog.UserID, handoverLog.MerchantNo).
		Where("created_at BETWEEN ? AND ?", handoverLog.StartAt, endAt).
		Count(&newCustomers).Error
}
