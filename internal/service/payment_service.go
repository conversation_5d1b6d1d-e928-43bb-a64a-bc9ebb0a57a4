package service

import (
	"context"
	"fmt"
	"regexp"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/cachex"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"time"

	"gorm.io/gorm"
)

// PaymentService 支付服务
type PaymentService struct {
	DB    *gorm.DB
	Trans util.Trans
	Cache cachex.Cacher
}

// GetPaymentQrcodeUrl 获取支付二维码地址
func (serv *PaymentService) GetPaymentQrcodeUrl(ctx context.Context, formItem *request.MerchantPaymentForm) (string, error) {

	if err := formItem.Validate(); err != nil {
		return "", err
	}

	// 设置支付信息缓存, 微信/支付宝扫码进来后创建数据
	err := serv.Cache.Set(ctx, consts.CacheNSForPayment, formItem.PaymentNo, formItem.String()) // todo 这里要加 过期时间

	if err != nil {
		return "", err
	}

	// 拼接支付地址
	payUrl := fmt.Sprintf("%s/?no=%s|%s", config.C.PaymentConfig.OnlinePayUrl, formItem.MerchantNo, formItem.PaymentNo)

	return payUrl, nil
}

// CreateMerchantPayment 新建商户支付记录
func (serv *PaymentService) CreateMerchantPayment(ctx context.Context, formItem *request.MerchantPaymentForm) (*model.MerchantPaymentModel, error) {
	if exists, err := serv.ExistsPaymentNo(ctx, formItem.PaymentNo, formItem.MerchantNo); err != nil {
		return nil, err
	} else if exists {
		return nil, errors.BadRequest("", "PaymentExists")
	}
	expiresAt := time.Now().Add(time.Duration(config.C.PaymentConfig.PaymentExpires) * time.Second)
	paymentInfo := &model.MerchantPaymentModel{
		ExpiresAt: &expiresAt,
	}

	if err := formItem.FillTo(paymentInfo); err != nil {
		return nil, err
	}

	err := util.GetDB(ctx, serv.DB).Create(paymentInfo).Error

	if err != nil {
		return nil, err
	}

	return paymentInfo, nil
}

// CreateOfflinePayment 新建商户离线支付记录
func (serv *PaymentService) CreateOfflinePayment(ctx context.Context, payment *model.MerchantPaymentModel) (*model.MerchantPaymentModel, error) {
	if exists, err := serv.ExistsPaymentNo(ctx, payment.PaymentNo, payment.MerchantNo); err != nil {
		return nil, err
	} else if exists {
		return nil, errors.BadRequest("", "PaymentExists")
	}

	err := util.GetDB(ctx, serv.DB).Create(payment).Error

	if err != nil {
		return nil, err
	}

	return payment, nil
}

// ListByOrderID 根据订单ID获取支付列表
func (serv *PaymentService) ListByOrderID(ctx context.Context, orderID int64, merchantNo string, status *int) ([]*model.MerchantPaymentModel, error) {
	var paymentInfos []*model.MerchantPaymentModel
	query := util.GetDB(ctx, serv.DB).
		Where("order_id=? AND merchant_no=?", orderID, merchantNo)

	if status != nil {
		query = query.Where("status = ?", *status)
	}

	err := query.Find(&paymentInfos).Error
	if err != nil {
		return nil, err
	}
	return paymentInfos, nil
}

// ListByOrderNo 根据订单号获取支付列表
func (serv *PaymentService) ListByOrderNo(ctx context.Context, orderNo string, merchantNo string, status *int) ([]*model.MerchantPaymentModel, error) {
	var paymentInfos []*model.MerchantPaymentModel
	query := util.GetDB(ctx, serv.DB).
		Where("order_no=? AND merchant_no=?", orderNo, merchantNo).
		Preload("PaymentType").Preload("Cashier")

	if status != nil {
		query = query.Where("status = ?", *status)
	}

	err := query.Find(&paymentInfos).Error
	if err != nil {
		return nil, err
	}
	return paymentInfos, nil
}

// UpdatePaymentStatus 更新支付状态
func (serv *PaymentService) UpdatePaymentStatus(ctx context.Context, paymentResult *schema.PaymentResult) (*model.MerchantPaymentModel, error) {
	paymentInfo, err := serv.GetByPaymentNo(ctx, paymentResult.PaymentNo, paymentResult.MerchantNo)
	if err != nil {
		return nil, err
	} else if paymentInfo == nil {
		return nil, errors.NotFound("", "PaymentNotFound")
	}
	// 如果已经支付过了，则不再更新
	if paymentInfo.Status == consts.PAY_STATUS_PAID {
		return paymentInfo, nil
	}
	// 更新支付状态
	if paymentResult.Status == consts.PAY_STATUS_PAID {
		return paymentInfo, util.GetDB(ctx, serv.DB).Model(paymentInfo).Updates(map[string]interface{}{
			"status":  paymentResult.Status,
			"paid_at": paymentResult.PaidAt,
		}).Error
	}
	return paymentInfo, nil
}

// RevertPayment 撤销支付（更新状态）
func (serv *PaymentService) RevertPayment(ctx context.Context, paymentNo string, merchantNo string) error {

	return util.GetDB(ctx, serv.DB).
		Model(model.MerchantPaymentModel{}).
		Where("merchant_no=? AND payment_no=? and status=?", merchantNo, paymentNo, consts.PAY_STATUS_PAID).
		Updates(map[string]interface{}{
			"status": consts.PAY_STATUS_REVERT,
		}).Error
}

// ExistsPaymentNo 判断支付单号是否存在
func (serv *PaymentService) ExistsPaymentNo(ctx context.Context, paymentNo string, merchantNo string) (bool, error) {
	return util.Exists(ctx, util.GetDB(ctx, serv.DB).Model(new(model.MerchantPaymentModel)).Where("merchant_no=? AND payment_no=?", merchantNo, paymentNo))
}

// GetByPaymentNo 根据paymentNo获取支付信息
func (serv *PaymentService) GetByPaymentNo(ctx context.Context, paymentNo string, merchantNo string) (*model.MerchantPaymentModel, error) {
	paymentInfo := &model.MerchantPaymentModel{}
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("merchant_no=? AND payment_no=?", merchantNo, paymentNo), paymentInfo)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return paymentInfo, nil
}

// ListByIdsForRefund 根据orderNo,paymentID获取支付信息
func (serv *PaymentService) ListByIdsForRefund(ctx context.Context, orderID int64, ids []int64, merchantNo string) ([]*model.MerchantPaymentModel, error) {
	var paymentInfos []*model.MerchantPaymentModel
	err := util.GetDB(ctx, serv.DB).
		Select("p.*, COALESCE(SUM(r.amount), 0) AS refund_amount").
		Table("merchant_payments p").
		Joins("LEFT JOIN merchant_refund_logs r ON p.payment_no = r.payment_no").
		Where("p.order_id=? AND p.id IN (?)", orderID, ids).
		Where("p.merchant_no=?", merchantNo).
		Where("p.status=?", consts.PAY_STATUS_PAID).
		Group("p.id").
		Find(&paymentInfos).Error
	if err != nil {
		return nil, err
	}
	return paymentInfos, nil
}

// ListForHandoverDetail 根据cashierId获取支付统计(用于交接班)
func (serv *PaymentService) ListForHandoverDetail(
	ctx context.Context, cashierId int64, merchantNo string,
	startDate time.Time, endDate time.Time) ([]*schema.CashierPaymentStatistics, error) {
	var paymentInfos []*schema.CashierPaymentStatistics
	err := util.GetDB(ctx, serv.DB).
		Select("p.payment_type_id,sum(p.amount) as order_amount, count(p.payment_type_id) as order_count, count(r.id) as refund_count, SUM(r.amount) AS refund_amount").
		Table("merchant_payments p").
		Joins("LEFT JOIN merchant_refund_logs r ON p.payment_no = r.payment_no").
		Where("p.cashier_id=?", cashierId).
		Where("p.merchant_no=?", merchantNo).
		Where("p.status=?", consts.PAY_STATUS_PAID).
		Where("p.created_at BETWEEN ? AND ?", startDate, endDate).
		Where("(r.created_at BETWEEN ? AND ?) OR r.created_at IS NULL", startDate, endDate).
		Group("p.payment_type_id").
		Find(&paymentInfos).Error
	if err != nil {
		return nil, err
	}
	return paymentInfos, nil
}

// GetByOutTradeNo 根据outTradeNo获取支付信息
func (serv *PaymentService) GetByOutTradeNo(ctx context.Context, paymentNo string) (*model.MerchantPaymentModel, error) {
	paymentInfo := &model.MerchantPaymentModel{}
	ok, err := util.FindOne(ctx, util.GetDB(ctx, serv.DB).Where("out_trade_no=?", paymentNo), paymentInfo)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return paymentInfo, nil
}

// GetPayTypeByAuthCode 根据authCode判断支付方式
func (serv *PaymentService) GetPayTypeByAuthCode(authCode string) (int64, error) {
	// 微信条码规则：18位纯数字，以10、11、12、13、14、15开头
	wechatRegex := regexp.MustCompile(`^(10|11|12|13|14|15)\d{16}$`)

	// 支付宝条码规则：25-30开头，长度16-24位
	alipayRegex := regexp.MustCompile(`^(25|26|27|28|29|30)\d{14,22}$`)

	if wechatRegex.MatchString(authCode) {
		return consts.PAY_TYPE_WECHAT, nil
	} else if alipayRegex.MatchString(authCode) {
		return consts.PAY_TYPE_ALIPAY, nil
	} else {
		return 0, errors.BadRequest("", "InvalidAuthCode")
	}
}

// GetPaymentTypeByID 根据ID获取支付方式
func (serv *PaymentService) GetPaymentTypeByID(ctx context.Context, id int64) (*model.PaymentTypeModel, error) {
	var paymentType model.PaymentTypeModel
	db := util.GetDB(ctx, serv.DB).Where("id = ?", id)
	ok, err := util.FindOne(ctx, db, &paymentType)
	if err != nil {
		return nil, err
	}
	if !ok {
		return nil, nil
	}
	return &paymentType, nil
}
