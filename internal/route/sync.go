package route

import (
	"context"
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/controller/v2/sync"
	"ros-api-go/internal/http/middleware"
)

// SyncRouterGroup 本地服务器同步路由组
type SyncRouterGroup struct {
	AreaController               *sync.AreaController
	TableController              *sync.TableController
	PrinterController            *sync.PrinterController
	FoodCategoryController       *sync.FoodCategory
	FoodsController              *sync.FoodsController
	FoodComboController          *sync.FoodComboController
	UserController               *sync.UserController
	MerchantInfoUpdateController *sync.MerchantInfoUpdateController
	MerchantConfigController     *sync.MerchantConfigController
	RemarkController             *sync.RemarkController
	RemarkCategoryController     *sync.RemarkCategoryController
	FoodPrinterController        *sync.FoodPrinterController
	MerchantRoleController       *sync.MerchantRoleController
	PermissionController         *sync.PermissionController
	PaymentTypeController        *sync.PaymentTypeController
	OrderSyncController          *sync.OrderSyncController
}

func (router *SyncRouterGroup) Register(ctx context.Context, group *gin.RouterGroup) {
	syncRouter := group.Group("sync").Use(middleware.ServerAuthMiddleware()) // ServerToken验证
	{
		syncRouter.GET("areas", router.AreaController.GetMerchantAreas)
		syncRouter.GET("tables", router.TableController.GetMerchantTables)
		syncRouter.GET("printers", router.PrinterController.GetMerchantPrinters)
		syncRouter.GET("food-categories", router.FoodCategoryController.GetMerchantFoodCategories)
		syncRouter.GET("foods", router.FoodsController.GetMerchantFoods)
		syncRouter.POST("foods/sell-clear-data", router.FoodsController.UpdateFoodSellClearData)
		syncRouter.GET("food-combos", router.FoodComboController.GetMerchantFoodCombos)
		syncRouter.GET("users", router.UserController.GetMerchantUsers)
		syncRouter.GET("remark-categories", router.RemarkCategoryController.GetMerchantRemarkCategories)
		syncRouter.GET("remarks", router.RemarkController.GetMerchantRemarks)
		syncRouter.GET("merchant-info-updates", router.MerchantInfoUpdateController.GetMerchantInfoUpdate)
		syncRouter.GET("merchant-configs", router.MerchantConfigController.GetMerchantConfigs)
		syncRouter.GET("food-printers", router.FoodPrinterController.GetMerchantFoodPrinters)
		syncRouter.GET("roles", router.MerchantRoleController.GetMerchantRoles)
		syncRouter.GET("permissions", router.PermissionController.GetAllPermissions)
		syncRouter.GET("payment-types", router.PaymentTypeController.GetPaymentTypes)
		syncRouter.POST("sync-order", router.OrderSyncController.SyncOrder)
		syncRouter.POST("sync-printer", router.PrinterController.SyncPrinter)
		syncRouter.POST("food-printers", router.FoodPrinterController.SyncFoodPrinter)
	}
}
