package route

import (
	"context"
	v2 "ros-api-go/internal/http/controller/v2"
	"ros-api-go/internal/http/middleware"

	"github.com/gin-gonic/gin"
)

type FoodRouterGroup struct {
	FoodController         *v2.FoodController
	FoodCategoryController *v2.FoodCategoryController
}

func (r *FoodRouterGroup) Register(ctx context.Context, router *gin.RouterGroup) {
	foods := router.Group("foods").Use(middleware.AuthMiddleware())
	{
		// 获取美食列表（分页）
		foods.GET("", r.FoodController.GetFoodsList)
		// 创建美食
		foods.POST("", middleware.CasbinMiddleware(), r.FoodController.CreateFood)
		// 更新美食
		foods.PUT("/:id", middleware.CasbinMiddleware(), r.FoodController.UpdateFood)
		// 获取双屏显示食品列表
		foods.GET("/dual", r.FoodController.FoodListForDualScreen)
		// 获取默认的美食图片
		foods.GET("/default-images", r.FoodController.GetDefaultFoodImages)
		// 修改美食状态
		foods.PUT("/:id/state", r.FoodController.ChangeState)
		// 修改美食是否支持扫码点单
		foods.PUT("/:id/support-scan-order", r.FoodController.ChangeSupportScanOrder)
		// 删除美食
		foods.DELETE("/:id", r.FoodController.Destroy)
	}

	foodCategory := router.Group("foodCategories").Use(middleware.AuthMiddleware())
	{
		// 获取菜品分类列表（分页）
		foodCategory.GET("", r.FoodCategoryController.GetFoodCategoryList)
		// 获取单个菜品分类
		foodCategory.GET("/:id", r.FoodCategoryController.GetFoodCategory)
		// 创建菜品分类
		foodCategory.POST("", r.FoodCategoryController.CreateFoodCategory)
		// 更新菜品分类
		foodCategory.PUT("/:id", r.FoodCategoryController.UpdateFoodCategory)
		// 删除菜品分类
		foodCategory.DELETE("/:id", r.FoodCategoryController.DeleteFoodCategory)
		// 更新菜品分类状态
		foodCategory.PUT("/:id/state", r.FoodCategoryController.UpdateFoodCategoryState)
	}
}
