package route

import (
	"context"
	"github.com/gin-gonic/gin"
	pay "ros-api-go/internal/http/controller/v2/pay"
	"ros-api-go/internal/http/middleware"
)

type PayRouterGroup struct {
	PaymentController         *pay.PaymentController
	PaymentReverseController  *pay.PaymentReverseController
	WechatPayController       *pay.WechatPayController
	AliPayController          *pay.AliPayController
	CustomerPayController     *pay.CustomerPayController
	RechargeController        *pay.RechargeController
	CheckUserMerchantProvider *middleware.CheckUserMerchantProvider
}

func (router *PayRouterGroup) Register(ctx context.Context, group *gin.RouterGroup) {
	payRouter := group.Group("/payment")
	{
		payRouter.POST("/wechat/payment-notify/:paymentNo", router.WechatPayController.HandlePaymentNotify)   // 微信支付结果通知
		payRouter.POST("/wechat/refund-notify/:paymentNo", router.WechatPayController.HandleRefundNotify)     // 微信退款结果通知
		payRouter.POST("/wechat/recharge-notify/:paymentNo", router.WechatPayController.HandleRechargeNotify) // 微信会员充值结果通知

		payRouter.POST("/alipay/payment-notify/:paymentNo", router.AliPayController.HandlePaymentNotify) // 支付宝支付结果通知
		//payRouter.POST("/alipay/refund-notify/:paymentNo", router.AliPayController.HandleRefundNotify)     // 支付宝退款结果通知
		payRouter.POST("/alipay/recharge-notify/:paymentNo", router.AliPayController.HandleRechargeNotify) // 支付宝会员充值结果通知

		payRouter.POST("/wechat/jsapi/:merchantNo/:paymentNo", router.WechatPayController.JsAPIPay)
		payRouter.POST("/alipay/jsapi/:merchantNo/:paymentNo", router.AliPayController.JsAPIPay)
	}
	payment := group.Group("/payment").Use(middleware.ServerAuthMiddleware())
	{
		payment.POST("qrcode", router.PaymentController.PaymentQrcodeUrl)       // 获取支付二维码
		payment.POST("micro-pay", router.PaymentController.MicroPay)            // 付款码支付
		payment.GET("query/:paymentNo", router.PaymentController.PaymentQuery)  // 支付查询
		payment.POST("reverse", router.PaymentReverseController.PaymentReverse) // 撤销支付
		payment.POST("customer-pay", router.CustomerPayController.Pay)
	}
	recharge := group.Group("/recharge").
		Use(middleware.AuthMiddleware()).
		Use(router.CheckUserMerchantProvider.Check())
	{
		recharge.POST("offline-pay", router.RechargeController.OfflinePay)       // 获取支付二维码
		recharge.POST("native-pay", router.RechargeController.NativePay)         // 获取支付二维码
		recharge.POST("micro-pay", router.RechargeController.MicroPay)           // 付款码支付
		recharge.GET("query/:paymentNo", router.RechargeController.PaymentQuery) // 支付查询
	}
}
