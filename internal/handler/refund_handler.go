package handler

import (
	"context"
	"errors"
	"go.uber.org/zap"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"time"
)

type RefundHandler struct {
	Trans                         util.Trans
	PaymentService                *service.PaymentService
	WechatPayService              *service.WechatPayService
	RefundLogService              *service.RefundLogService
	RefundBatchService            *service.RefundBatchService
	CustomerService               *service.CustomerService
	DebtHolderService             *service.DebtHolderService
	CustomerConsumptionLogService *service.CustomerConsumptionLogService
	DebtTransactionService        *service.DebtTransactionService
}

// HandleRefundByRefundLog 根据退款记录进行退款处理(异步调用)
func (handler *RefundHandler) HandleRefundByRefundLog(ctx context.Context, refundLog *model.MerchantRefundLogModel, cashierID int64) {

	var refundResult *schema.RefundResult
	var err error
	bgctx := util.NewTraceID(context.Background(), util.FromTraceID(ctx)) // 带上traceID
	defer func() {
		if r := recover(); r != nil {
			logging.Context(bgctx).Error("HandleRefundByRefundLog 处理退款失败（panic）",
				zap.Int64("refund_log_id", refundLog.ID),
				zap.String("merchant_no", refundLog.MerchantNo),
				zap.String("order_no", refundLog.OrderNo),
				zap.Any("error", r))
		}
	}()
	logging.Context(bgctx).Info("==========退款处理开始==========",
		zap.Int64("refund_log_id", refundLog.ID),
		zap.String("merchant_no", refundLog.MerchantNo),
		zap.String("order_no", refundLog.OrderNo),
	)

	paymentType, err := handler.PaymentService.GetPaymentTypeByID(bgctx, refundLog.PaymentTypeID)
	if err != nil || paymentType == nil {
		logging.Context(bgctx).Error("获取支付方式失败",
			zap.Int64("refund_log_id", refundLog.ID),
			zap.String("merchant_no", refundLog.MerchantNo),
			zap.String("order_no", refundLog.OrderNo),
			zap.Error(err))
		return
	}

	err = handler.Trans.Exec(ctx, func(ctx context.Context) error {
		// 线下支付
		if paymentType.Online == 0 {
			refundResult, err = handler.HandleOfflineRefund(bgctx, refundLog, cashierID)
		} else {
			switch refundLog.PaymentTypeID {
			case consts.PAY_TYPE_WECHAT:
				refundResult, err = handler.WechatPayService.RefundByRefundLog(bgctx, refundLog)
			case consts.PAY_TYPE_ALIPAY:
				logging.Context(bgctx).Warn("暂不支持支付宝退款")
				return errors.New("暂不支持支付宝退款")
			case consts.PAY_TYPE_VIPCARD:
				refundResult, err = handler.HandleVipRefund(bgctx, refundLog, cashierID)
			case consts.PAY_TYPE_DEBT:
				refundResult, err = handler.HandleDebtRefund(bgctx, refundLog, cashierID)
			default:
				logging.Context(bgctx).Warn("退款失败，不支持的支付类型")
			}
		}

		if err != nil {
			logging.Context(bgctx).Error("=========退款失败=========",
				zap.Int64("refund_log_id", refundLog.ID),
				zap.String("merchant_no", refundLog.MerchantNo),
				zap.String("order_no", refundLog.OrderNo),
				zap.String("message", err.Error()),
			)
			// 更新退款状态
			refundResult = &schema.RefundResult{
				Status:      consts.REFUND_STATUS_FAILED,
				MerchantNo:  refundLog.MerchantNo,
				PaymentNo:   refundLog.PaymentNo,
				OutTradeNo:  refundLog.OutTradeNo,
				OutRefundNo: refundLog.OutRefundNo,
				TradeState:  "FAILED",
				TradeDesc:   err.Error(),
			}
		}

		if refundResult != nil {
			_, err = handler.RefundLogService.UpdateRefundStatus(bgctx, refundResult)
			if err != nil {
				logging.Context(bgctx).Warn("更新RefundLog信息失败", zap.Error(err))
				return err
			}
			// 如果有批次号，则更新批次状态
			if refundLog.BatchID > 0 {
				err = handler.RefundBatchService.UpdateRefundStatus(bgctx, refundLog.MerchantNo, refundLog.BatchID, refundResult)
				if err != nil {
					logging.Context(bgctx).Warn("更新RefundBatch信息失败", zap.Error(err))
					return err
				}
			}
			// 如果时支付撤销
			if refundLog.RefundType == consts.REFUND_TYPE_PAYMENT {
				err = handler.PaymentService.RevertPayment(bgctx, refundLog.PaymentNo, refundLog.MerchantNo)
				if err != nil {
					logging.Context(bgctx).Warn("更新MerchantPayment信息失败", zap.Error(err))
					return err
				}
			}

		}
		return nil
	})
	if err != nil {
		logging.Context(bgctx).Error("=========退款失败=========",
			zap.Int64("refund_log_id", refundLog.ID),
			zap.String("merchant_no", refundLog.MerchantNo),
			zap.String("order_no", refundLog.OrderNo),
			zap.String("message", err.Error()),
		)
		return
	}

	logging.Context(bgctx).Info("==========退款处理结束==========",
		zap.Int64("refund_log_id", refundLog.ID),
		zap.String("merchant_no", refundLog.MerchantNo),
		zap.String("order_no", refundLog.OrderNo),
	)
}

// HandleVipRefund 处理VIP退款
func (handler *RefundHandler) HandleVipRefund(ctx context.Context, refundLog *model.MerchantRefundLogModel, cashierID int64) (*schema.RefundResult, error) {

	amountFloat := util.DivideFloat(float64(refundLog.Amount), 100)

	logging.Context(ctx).Info("VIP退款开始处理",
		zap.Int64("refund_log_id", refundLog.ID),
		zap.String("merchant_no", refundLog.MerchantNo),
		zap.String("order_no", refundLog.OrderNo),
		zap.Float64("refund_amount", amountFloat),
	)
	// 退款给用户余额
	payment, err := handler.PaymentService.GetByPaymentNo(ctx, refundLog.PaymentNo, refundLog.MerchantNo)
	if err != nil {
		return nil, err
	}
	if payment == nil {
		return nil, errors.New("VIP退款找不到支付信息")
	}
	if payment.CustomerID == 0 {
		return nil, errors.New("VIP退款支付信息缺失客户ID")
	}
	customer, err := handler.CustomerService.GetByID(ctx, payment.CustomerID)
	if err != nil {
		return nil, err
	}
	if customer == nil {
		return nil, errors.New("VIP退款找不到客户信息")
	}
	var refundResult *schema.RefundResult
	err = handler.Trans.Exec(ctx, func(ctx context.Context) error {
		customer.Balance = util.AddFloat(customer.Balance, amountFloat)
		err = handler.CustomerService.IncreaseBalance(ctx, customer.ID, amountFloat)
		if err != nil {
			return err
		}
		// 记录消费日志
		err = handler.CustomerConsumptionLogService.Create(ctx, &model.CustomerConsumptionLogModel{
			CustomerID:    customer.ID,
			MerchantNo:    refundLog.MerchantNo,
			OrderID:       refundLog.OrderID,
			Type:          model.ConsumptionTypeRefund,
			Balance:       customer.Balance,
			Amount:        amountFloat,
			PaymentTypeID: refundLog.PaymentTypeID,
			CashierID:     cashierID,
		})
		if err != nil {
			return err
		}
		now := time.Now()
		refundResult = &schema.RefundResult{
			Status:      consts.REFUND_STATUS_SUCCESS,
			MerchantNo:  refundLog.MerchantNo,
			PaymentNo:   refundLog.PaymentNo,
			OutTradeNo:  refundLog.OutTradeNo,
			OutRefundNo: refundLog.OutRefundNo,
			TradeState:  "SUCCESS",
			TradeType:   "VIPCARD",
			RefundAt:    &now,
			TradeDesc:   "VIP退款成功",
			RefundID:    refundLog.RefundID,
		}
		return nil
	})

	if err != nil {
		return nil, err
	}
	logging.Context(ctx).Info("VIP退款处理完成",
		zap.Int64("refund_log_id", refundLog.ID),
		zap.String("merchant_no", refundLog.MerchantNo),
		zap.String("order_no", refundLog.OrderNo),
		zap.Float64("refund_amount", amountFloat),
	)
	return refundResult, nil
}

// HandleDebtRefund 处理赊账退款
func (handler *RefundHandler) HandleDebtRefund(ctx context.Context, refundLog *model.MerchantRefundLogModel, cashierID int64) (*schema.RefundResult, error) {

	amountFloat := util.DivideFloat(float64(refundLog.Amount), 100)

	logging.Context(ctx).Info("赊账退款开始处理",
		zap.Int64("refund_log_id", refundLog.ID),
		zap.String("merchant_no", refundLog.MerchantNo),
		zap.String("order_no", refundLog.OrderNo),
		zap.Float64("refund_amount", amountFloat),
	)
	// 退款给用户余额
	payment, err := handler.PaymentService.GetByPaymentNo(ctx, refundLog.PaymentNo, refundLog.MerchantNo)
	if err != nil {
		return nil, err
	}
	if payment == nil {
		return nil, errors.New("赊账退款找不到支付信息")
	}
	if payment.CustomerID == 0 {
		return nil, errors.New("赊账退款支付信息缺失客户ID")
	}
	holder, err := handler.DebtHolderService.GetByID(ctx, payment.MerchantNo, payment.CustomerID)
	if err != nil {
		return nil, err
	}
	if holder == nil {
		return nil, errors.New("赊账退款找不到赊账人信息")
	}
	var refundResult *schema.RefundResult
	err = handler.Trans.Exec(ctx, func(ctx context.Context) error {
		holder.Balance -= refundLog.Amount
		err = handler.DebtHolderService.DecreaseBalance(ctx, holder.ID, refundLog.Amount)
		if err != nil {
			return err
		}
		// 记录消费日志
		err = handler.DebtTransactionService.Create(ctx, &model.DebtTransactionModel{
			MerchantNo:    refundLog.MerchantNo,
			HolderID:      holder.ID,
			OrderNo:       &refundLog.OrderNo,
			OrderID:       &refundLog.OrderID,
			Amount:        -1 * refundLog.Amount, // 负数表示退款/充值
			Type:          model.DebtTransactionTypeRefund,
			Balance:       holder.Balance,
			PaymentTypeID: refundLog.PaymentTypeID,
			CashierID:     cashierID,
		})
		if err != nil {
			return err
		}
		now := time.Now()
		refundResult = &schema.RefundResult{
			Status:      consts.REFUND_STATUS_SUCCESS,
			MerchantNo:  refundLog.MerchantNo,
			PaymentNo:   refundLog.PaymentNo,
			OutTradeNo:  refundLog.OutTradeNo,
			OutRefundNo: refundLog.OutRefundNo,
			TradeState:  "SUCCESS",
			TradeType:   "DEBT_REFUND",
			RefundAt:    &now,
			TradeDesc:   "赊账退款成功",
			RefundID:    refundLog.RefundID,
		}
		return nil
	})

	if err != nil {
		return nil, err
	}
	logging.Context(ctx).Info("赊账退款处理完成",
		zap.Int64("refund_log_id", refundLog.ID),
		zap.String("merchant_no", refundLog.MerchantNo),
		zap.String("order_no", refundLog.OrderNo),
		zap.Float64("refund_amount", amountFloat),
	)
	return refundResult, nil
}

func (handler *RefundHandler) HandleOfflineRefund(ctx context.Context, refundLog *model.MerchantRefundLogModel, cashierID int64) (*schema.RefundResult, error) {

	amountFloat := util.DivideFloat(float64(refundLog.Amount), 100)

	logging.Context(ctx).Info("开始线下退款处理",
		zap.Int64("refund_log_id", refundLog.ID),
		zap.String("merchant_no", refundLog.MerchantNo),
		zap.String("order_no", refundLog.OrderNo),
		zap.Float64("refund_amount", amountFloat),
	)
	// 退款给用户余额
	payment, err := handler.PaymentService.GetByPaymentNo(ctx, refundLog.PaymentNo, refundLog.MerchantNo)
	if err != nil {
		return nil, err
	}
	if payment == nil {
		return nil, errors.New("找不到支付信息")
	}
	now := time.Now()
	refundResult := &schema.RefundResult{
		Status:      consts.REFUND_STATUS_SUCCESS,
		MerchantNo:  refundLog.MerchantNo,
		PaymentNo:   refundLog.PaymentNo,
		OutTradeNo:  refundLog.OutTradeNo,
		OutRefundNo: refundLog.OutRefundNo,
		TradeState:  "SUCCESS",
		TradeType:   util.GetOfflinePaymentTradeType(payment.PaymentTypeID),
		RefundAt:    &now,
		TradeDesc:   "线下支付退款成功",
		RefundID:    refundLog.RefundID,
	}
	logging.Context(ctx).Info("完成线下支付退款",
		zap.Int64("refund_log_id", refundLog.ID),
		zap.String("merchant_no", refundLog.MerchantNo),
		zap.String("order_no", refundLog.OrderNo),
		zap.Float64("refund_amount", amountFloat),
	)
	return refundResult, nil
}
