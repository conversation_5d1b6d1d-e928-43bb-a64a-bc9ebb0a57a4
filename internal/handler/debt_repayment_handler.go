package handler

import (
	"context"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"
)

type DebtRepaymentHandler struct {
	Trans                     util.Trans
	MerchantService           *service.MerchantService
	DebtHolderService         *service.DebtHolderService
	PaymentLogService         *service.PaymentLogService
	DebtRepaymentOrderService *service.DebtRepaymentOrderService
	DebtTransactionService    *service.DebtTransactionService
}

// HandleOnlinePaySuccess 线上支付成功
func (handler *DebtRepaymentHandler) HandleOnlinePaySuccess(
	ctx context.Context, holder *model.DebtHolderModel,
	repaymentOrder *model.DebtRepaymentOrderModel, paymentResult *schema.PaymentResult) error {
	// 更新支付信息
	err := handler.Trans.Exec(ctx, func(ctx context.Context) error {
		// 更新支付记录
		if _, err := handler.PaymentLogService.UpdatePaymentStatus(ctx, paymentResult); err != nil {
			return err
		}
		// 更新充值订单状态
		if err := handler.DebtRepaymentOrderService.UpdateStatus(ctx, repaymentOrder.ID, model.DebtRepaymentOrderStatusSuccess); err != nil {
			return err
		}
		// 更新赊账人余额
		return handler.UpdateDebtHolderBalance(ctx, holder, repaymentOrder, paymentResult.PaymentTypeID)
	})
	if err != nil {
		return err
	}
	return nil
}

// UpdateDebtHolderBalance 更新余额
func (handler *DebtRepaymentHandler) UpdateDebtHolderBalance(
	ctx context.Context, holder *model.DebtHolderModel,
	repaymentOrder *model.DebtRepaymentOrderModel, paymentTypeId int64) error {
	// 记录消费日志
	transaction := &model.DebtTransactionModel{
		MerchantNo:    repaymentOrder.MerchantNo,
		HolderID:      repaymentOrder.HolderID,
		Type:          model.DebtTransactionTypeRepayment,
		OrderID:       &repaymentOrder.ID,
		OrderNo:       &repaymentOrder.No,
		Balance:       holder.Balance - repaymentOrder.Amount,
		Amount:        -1 * repaymentOrder.Amount, // 负数表示还款
		CashierID:     repaymentOrder.CashierID,
		PaymentTypeID: paymentTypeId,
	}
	if err := handler.DebtTransactionService.Create(ctx, transaction); err != nil {
		return err
	}
	// 更新余额
	return handler.DebtHolderService.DecreaseBalance(ctx, repaymentOrder.HolderID, repaymentOrder.Amount)
}
