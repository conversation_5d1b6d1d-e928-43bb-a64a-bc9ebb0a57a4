package request

type DebtRepaymentRequest struct {
	HolderID      int64  `json:"holder_id" binding:"required"`    // 赊账人ID
	Amount        int64  `json:"amount" binding:"required,min=1"` // 支付金额，单位分
	CashierID     int64  `json:"-"`                               // 收银员ID
	MerchantNo    string `json:"-"`                               // 商家编号，由系统自动填充
	PaymentTypeID int64  `json:"-"`                               // 支付方式，1：微信支付，3 现金支付，10：支付宝支付 .由系统判断填充
}

type DebtRepaymentMicroPayForm struct {
	DebtRepaymentRequest
	AuthCode string `json:"auth_code" binding:"required"` // 付款码编号
}

type DebtRepaymentOfflineForm struct {
	DebtRepaymentRequest
	PaymentTypeID int64 `json:"payment_type_id" binding:"required"` // 付款码编号
}
