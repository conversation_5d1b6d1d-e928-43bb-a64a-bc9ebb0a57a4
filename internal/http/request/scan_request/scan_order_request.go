package scan_request

import (
	"ros-api-go/internal/http/request/order_request"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/util"
)

type ScanOrderListRequest struct {
	util.PaginationParam
	WechatUserId int64 `form:"-"` // 微信用户ID(系统填充)
	State        int   `form:"state" binding:"omitempty,min=0"`
}

type ScanOrderCreateRequest struct {
	MerchantNo     string                       `json:"-"`                                                       // 商户号 (系统填充)
	WechatUserID   int64                        `json:"wechat_user_id"`                                          // 微信用户ID(系统填充)
	OpenID         string                       `json:"open_id"`                                                 // 微信用户OpenID(系统填充)
	CustomersCount int                          `json:"customers_count" binding:"required,number,min=1,max=999"` // 顾客数量
	TableID        int64                        `json:"table_id" binding:"required,number,min=1"`                // 餐桌ID
	Terminal       string                       `json:"terminal" binding:"required,oneof=wechat alipay"`         // 终端号
	PaymentID      int                          `json:"payment_id" binding:"required,number,min=1"`              // 支付方式ID
	OrderDetails   []*order_request.OrderDetail `json:"order_details" binding:"required,min=1,dive"`             // 订单详情
	Remark         *string                      `json:"remark" binding:"max=255"`                                // 备注
}

type ScanOrderDetail struct {
	FoodID     int               `json:"food_id" binding:"required,number,min=1"`     // 菜品ID
	FoodsCount float64           `json:"foods_count" binding:"required,number,min=1"` // 菜品数量
	Remarks    string            `json:"remarks" binding:"max=255"`                   // 备注
	ComboInfo  []model.ComboInfo `json:"combo_info"`                                  // 套餐信息
}
