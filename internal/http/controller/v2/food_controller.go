package v2

import (
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/http/resource/order_resource"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
	"strconv"

	"github.com/gin-gonic/gin"
)

type FoodController struct {
	FoodsService              *service.FoodsService
	MerchantInfoUpdateHandler *handler.MerchantInfoUpdateHandler
}

// GetMerchantAvailableFoods 获取商家正常的美食列表
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 获取商家正常的美食列表
// @Produce json
// @Param food_category_id query int false "食物类别ID"
// @Param keyword query string false "搜索关键词"
// @Param sell_clear_all query string false "是否清理所有销售标志"
// @Success 200 {object} util.ResponseResult{ Data=[]resource.FoodResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /cloud/api/v2/foods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodController) GetMerchantAvailableFoods(c *gin.Context) {
	ctx := c.Request.Context()
	MerchantNo := util.GetMerchantNo(c)

	// 从查询参数中获取食物类别ID
	catId := util.StrToInt64(c.Query("food_category_id"))
	// 从查询参数中获取搜索关键词
	keyword := c.Query("keyword")
	// 从查询参数中获取是否清理所有销售标志
	sellClearAll := c.Query("sell_clear_all")

	result, err := ctrl.FoodsService.GetMerchantAvailableFoods(ctx, MerchantNo, catId, keyword, sellClearAll)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&order_resource.FoodResource{}).Collection(&ctx, result))
}

// GetFoodsList 获取美食列表
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 获取美食列表
// @Produce json
// @Param page query int false "页码"
// @Param limit query int false "每页数量"
// @Param food_category_id query int false "食物类别ID"
// @Param keyword query string false "搜索关键词"
// @Param state query int false "状态"
// @Param sell_clear_all query string false "是否沽清列表"
// @Param support_scan_order query bool false "是否支持扫码点单"
// @Success 200 {object} util.ResponseResult{ Data=[]resource.FoodResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foods [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
func (ctrl *FoodController) GetFoodsList(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req request.FoodListRequest
	if err := util.ParseQuery(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	req.MerchantNo = merchantNo
	result, err := ctrl.FoodsService.GetFoodsList(ctx, &req)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, (&resource.FoodResource{}).Collection(result))
}

// CreateFood 创建美食
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 创建美食
// @Param request body request.CreateFoodRequest true "创建美食请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{ Data=resource.FoodResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foods [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodController) CreateFood(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	var req request.CreateFoodRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, err := ctrl.FoodsService.CreateFood(ctx, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, model.FoodModel{})
}

// UpdateFood 更新美食
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 更新美食
// @Param id path int true "美食ID"
// @Param request body request.UpdateFoodRequest true "更新美食请求参数"
// @Produce json
// @Success 200 {object} util.ResponseResult{ Data=resource.FoodResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foods/{id} [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodController) UpdateFood(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	id, err := strconv.ParseInt(c.Param("id"), 10, 64)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	var req request.UpdateFoodRequest
	if err := util.ParseJSON(c, &req); err != nil {
		util.ResError(c, err)
		return
	}

	_, err = ctrl.FoodsService.UpdateFood(ctx, id, &req, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, model.FoodModel{})
}

// ChangeState 修改美食状态
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 修改美食状态
// @Param id path int true "美食ID"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foods/{id}/state [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodController) ChangeState(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)
	foodId := util.StrToInt64(c.Param("id"))

	if foodId <= 0 {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	_, err := ctrl.FoodsService.ChangeFoodState(ctx, merchantNo, foodId)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, model.FoodModel{})
}

// ChangeSupportScanOrder 修改美食是否支持扫码点单
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 修改美食是否支持扫码点单
// @Param id path int true "美食ID"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foods/{id}/support-scan-order [put]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodController) ChangeSupportScanOrder(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)
	foodId := util.StrToInt64(c.Param("id"))

	if foodId <= 0 {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	_, err := ctrl.FoodsService.ChangeFoodSupportScanOrder(ctx, merchantNo, foodId)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, model.FoodModel{})
}

// Destroy 删除美食
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 删除美食
// @Param id path int true "美食ID"
// @Produce json
// @Success 200 {object} util.ResponseResult
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foods/{id} [delete]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodController) Destroy(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)
	foodId := util.StrToInt64(c.Param("id"))

	if foodId <= 0 {
		util.ResError(c, errors.BadRequest("", "InvalidID"))
		return
	}

	err := ctrl.FoodsService.DeleteFood(ctx, merchantNo, foodId)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResOK(c)
	ctrl.MerchantInfoUpdateHandler.UpdateMerchantInfoUpdates(merchantNo, model.FoodModel{})
}

// FoodListForDualScreen 获取双屏显示食品列表
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 获取双屏显示食品列表
// @Produce json
// @Success 200 {object} util.ResponseResult{ Data=[]resource.FoodForDualScreenResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foods/dual [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
func (ctrl *FoodController) FoodListForDualScreen(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)

	// 调用服务层获取数据（使用缓存）
	foods, err := ctrl.FoodsService.GetFoodListForDualScreen(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 使用资源转换器格式化响应
	util.ResSuccess(c, (&resource.FoodForDualScreenResource{}).Collection(foods))
}

// GetDefaultFoodImages 获取平台默认美食图片
//
// @Tags 美食管理
// @Security ApiAuthToken
// @Summary 获取平台默认美食图片
// @Produce json
// @Success 200 {object} util.ResponseResult{ Data=[]resource.DefaultImageResource }
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/foods/default-images [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/06/27 10:00"]
// @X-Version ["2.0"]
func (ctrl *FoodController) GetDefaultFoodImages(c *gin.Context) {
	ctx := c.Request.Context()
	images, err := ctrl.FoodsService.GetDefaultFoodImages(ctx)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, (&resource.DefaultImageResource{}).Collection(images))
}
