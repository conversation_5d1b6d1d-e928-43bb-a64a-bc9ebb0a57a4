package sync

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type FoodCategory struct {
	FoodCategoryService *service.FoodCategoryService
}

// GetMerchantFoodCategories 获取商户的食品分类列表
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 获取商户的食品分类列表
// @Success 200 {object} util.ResponseResult{data=[]resource.FoodCategoryResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/food-categories [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/3 16:19"]
// @X-Version ["2.0"]
func (ctrl *FoodCategory) GetMerchantFoodCategories(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodCategoryService.GetMerchantFoodCategories(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, (&resource.FoodCategoryResource{}).Collection(result))
}
