package sync

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type FoodComboController struct {
	FoodComboService *service.FoodComboService
}

// GetMerchantFoodCombos 同步菜品套餐接口
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步菜品套餐接口
// @Success 200 {object} util.ResponseResult{data=[]resource.FoodComboResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/food-combos [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/2 16:21"]
// @X-Version ["2.0"]
func (ctrl *FoodComboController) GetMerchantFoodCombos(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.FromServerMerchantNo(ctx)

	result, err := ctrl.FoodComboService.GetMerchantFoodCombos(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	comboResource := resource.FoodComboResource{}
	util.ResSuccess(c, comboResource.Collection(result))
}
