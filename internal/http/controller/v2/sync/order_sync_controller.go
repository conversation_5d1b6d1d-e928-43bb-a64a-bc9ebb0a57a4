package sync

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/request/order_request"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"
)

type OrderSyncController struct {
	OrderSyncService *service.OrderSyncService
}

// SyncOrder 同步订单(本地 -> 云端)
//
// @Tags 同步接口
// @Security ServerTokenAuth
// @Summary 同步本地订单到云端接口
// @Success 200 {object} util.ResponseResult{message=string}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/sync-order [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/12 16:30"]
// @X-Version ["2.0"]
func (ctrl *OrderSyncController) SyncOrder(c *gin.Context) {
	// 1. 获取参数
	ctx := c.Request.Context()
	var formItem order_request.OrderSyncRequest
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}

	// 获取merchantNo
	merchantNo := util.FromServerMerchantNo(ctx)

	// 验证参数
	if err := formItem.Validate(ctx); err != nil {
		util.ResError(c, err)
		return
	}
	// 2. 调用service层的Sync方法
	err := ctrl.OrderSyncService.SyncOrder(ctx, merchantNo, &formItem)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 3. 返回结果
	util.ResOK(c, "OperateSuccess")
}
