package order

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request/order_request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

type PaymentReverseController struct {
	Trans              *util.Trans
	PaymentLogService  *service.PaymentLogService
	PaymentService     service.PaymentService
	PaymentTypeService *service.PaymentTypeService
	WechatPayService   *service.WechatPayService
	RefundLogService   *service.RefundLogService
	MerchantService    *service.MerchantService
	RefundHandler      *handler.RefundHandler
}

// PaymentReverse 撤销支付(全额退款)
//
// @Tags 支付相关接口
// @Security ServerTokenAuth
// @Summary 撤销支付(全额退款)
// @Params body request.PaymentRefundForm true "支付信息编号"
// @Success 200 {object} util.ResponseResult{data=schema.RefundResult}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/reverse [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/23 16:30"]
// @X-Version ["2.0"]
func (ctrl *PaymentReverseController) PaymentReverse(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem order_request.PaymentReverseRequest
	err := util.ParseJSON(c, &formItem)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取MerchantNO
	merchantNo := c.Request.Header.Get("MerchantNo")
	userID := util.FromUserID(ctx)

	// 获取商户信息
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取支付记录
	payment, err := ctrl.PaymentService.GetByPaymentNo(ctx, formItem.PaymentNo, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if payment == nil || payment.MerchantNo != merchantNo {
		util.ResError(c, errors.BadRequest("1001", "PaymentNotFound"))
		return
	}

	// 如果已经撤销，则直接返回成功
	if payment.Status == consts.PAY_STATUS_REVERT {
		util.ResOK(c, "RefundSuccess")
		return
	}

	// 如果未支付，则直接返回失败
	if payment.Status != consts.PAY_STATUS_PAID {
		util.ResError(c, errors.BadRequest("", "PaymentNotPaid"))
		return
	}

	paymentType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, payment.PaymentTypeID)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 如果是线下支付，直接更新状态为已撤销
	if paymentType.Online == 0 {
		if err := ctrl.PaymentService.RevertPayment(ctx, payment.PaymentNo, payment.MerchantNo); err != nil {
			util.ResError(c, err)
			return
		}
		util.ResOK(c, "RefundSuccess")
		return
	}

	// 如果是会员卡支付，则调用vipPaymentReverse
	if paymentType.ID == consts.PAY_TYPE_VIPCARD {
		ctrl.vipPaymentReverse(c, merchant, payment, userID)
		return
	}

	// 其他第三方平台支付则走第三方退款

	// 获取支付记录
	paymentLog, err := ctrl.PaymentLogService.GetPaidRecordByPaymentNo(ctx, formItem.PaymentNo, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if paymentLog == nil {
		util.ResError(c, errors.BadRequest("1002", "PaymentNotFound"))
		return
	}

	// 订单号是否一致
	if formItem.OrderNo != paymentLog.OrderNo {
		util.ResError(c, errors.BadRequest("1003", "PaymentNotFound"))
		return
	}
	// 判断支付状态
	if paymentLog.Status == consts.PAY_STATUS_UNPAID {
		util.ResError(c, errors.BadRequest("", "PaymentNotPaid"))
		return
	}

	// 查找退款记录
	existsRefund, err := ctrl.RefundLogService.GetByPaymentNo(ctx, formItem.PaymentNo, consts.REFUND_TYPE_PAYMENT)

	// 如果有退款记录，则直接返回成功
	if existsRefund != nil {
		util.ResOK(c, "RefundSuccess")
		// 如果退款记录状态为处理中/失败，则重新处理退款
		if existsRefund.RefundStatus == consts.REFUND_STATUS_WAITING || existsRefund.RefundStatus == consts.REFUND_STATUS_FAILED {
			go ctrl.RefundHandler.HandleRefundByRefundLog(ctx, existsRefund, userID)
		}
		return
	}

	// 创建退款记录(这里是全部退款, 没有订单号/批次号)
	refundLog, err := ctrl.RefundLogService.CreateFromPaymentLog(ctx, merchant, 0, 0, paymentLog, paymentLog.Amount, consts.REFUND_TYPE_PAYMENT)

	if err != nil {
		util.ResError(c, errors.BadRequest("", "RefundFailed"))
		return
	}
	// 异步处理退款
	go ctrl.RefundHandler.HandleRefundByRefundLog(ctx, refundLog, userID)
	util.ResOK(c, "RefundSuccess")
}

// vipPaymentReverse 会员卡支付撤销支付(全额退款)
func (ctrl *PaymentReverseController) vipPaymentReverse(c *gin.Context, merchant *model.MerchantModel, payment *model.MerchantPaymentModel, cashierID int64) {
	ctx := c.Request.Context()

	// 查找退款记录
	refundLog, err := ctrl.RefundLogService.GetByPaymentNo(ctx, payment.PaymentNo, consts.REFUND_TYPE_PAYMENT)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 如果没有则创建
	if refundLog == nil {
		// 记录退款日志(这里是全部退款, 没有订单号/批次号)
		refundLog, err = ctrl.RefundLogService.CreateFromPayment(ctx, merchant, 0, payment, payment.Amount, consts.REFUND_TYPE_PAYMENT)
		if err != nil {
			util.ResError(c, errors.BadRequest("", "RefundFailed"))
			return
		}
	}

	util.ResOK(c, "RefundSuccess")
	if refundLog.RefundStatus == consts.REFUND_STATUS_WAITING || refundLog.RefundStatus == consts.REFUND_STATUS_FAILED {
		go ctrl.RefundHandler.HandleRefundByRefundLog(ctx, refundLog, cashierID)
	}
}
