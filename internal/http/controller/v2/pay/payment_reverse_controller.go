package pay

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

type PaymentReverseController struct {
	Trans             *util.Trans
	PaymentLogService *service.PaymentLogService
	PaymentService    service.PaymentService
	WechatPayService  *service.WechatPayService
	RefundLogService  *service.RefundLogService
	MerchantService   *service.MerchantService
	RefundHandler     *handler.RefundHandler
}

// PaymentReverse 撤销支付(全额退款)
//
// @Tags 支付相关接口
// @Security ServerTokenAuth
// @Summary 撤销支付(全额退款)
// @Params body request.PaymentRefundForm true "支付信息编号"
// @Success 200 {object} util.ResponseResult{data=schema.RefundResult}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/reverse [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/23 16:30"]
// @X-Version ["2.0"]
func (ctrl *PaymentReverseController) PaymentReverse(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.PaymentRefundForm
	err := util.ParseJSON(c, &formItem)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取MerchantNO
	formItem.MerchantNo = util.FromServerMerchantNo(ctx)

	// 获取商户信息
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, formItem.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取支付信息
	payment, err := ctrl.PaymentService.GetByPaymentNo(ctx, formItem.PaymentNo, formItem.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 校验支付信息
	if payment == nil || payment.MerchantNo != formItem.MerchantNo {
		util.ResError(c, errors.BadRequest("", "PaymentNotFound"))
		return
	}

	// 如果已经撤销，则直接返回成功
	if payment.Status == consts.PAY_STATUS_REVERT {
		util.ResOK(c, "RefundSuccess")
		return
	}

	// 如果未支付，则直接返回失败
	if payment.Status != consts.PAY_STATUS_PAID {
		util.ResError(c, errors.BadRequest("", "PaymentNotPaid"))
		return
	}

	// VIP 支付撤回
	if payment.PaymentTypeID == consts.PAY_TYPE_VIPCARD || payment.PaymentTypeID == consts.PAY_TYPE_DEBT {
		ctrl.systemPaymentReverse(c, merchant, payment, formItem.CashierID)
		return
	}

	// 如果是微信/支付宝的撤回
	// 如果未开通微信特约商户
	if merchant.SubMchID == "" {
		util.ResError(c, errors.BadRequest("", "MerchantNotSupportWechatPay"))
		return
	}

	// 获取支付记录
	paymentLog, err := ctrl.PaymentLogService.GetByPaymentNo(ctx, formItem.PaymentNo, formItem.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if paymentLog == nil {
		util.ResError(c, errors.BadRequest("", "PaymentNotFound"))
		return
	}

	err = formItem.Validate(ctx, paymentLog)
	if err != nil {
		util.ResError(c, err)
		return
	}

	existsRefund, err := ctrl.RefundLogService.GetByPaymentNo(ctx, formItem.MerchantNo, formItem.PaymentNo, consts.REFUND_TYPE_PAYMENT)

	// 如果有退款记录，则直接返回成功
	if existsRefund != nil {
		util.ResOK(c, "RefundSuccess")
		if existsRefund.RefundStatus == consts.REFUND_STATUS_WAITING || existsRefund.RefundStatus == consts.REFUND_STATUS_FAILED {
			go ctrl.RefundHandler.HandleRefundByRefundLog(ctx, existsRefund, formItem.CashierID)
		}
		return
	}

	// 创建退款记录(这里是全部退款, 没有订单号/批次号)
	refundLog, err := ctrl.RefundLogService.CreateFromPaymentLog(ctx, merchant, 0, 0, paymentLog, paymentLog.Amount, consts.REFUND_TYPE_PAYMENT)

	if err != nil {
		util.ResError(c, errors.BadRequest("", "RefundFailed"))
		return
	}
	// 异步处理退款
	go ctrl.RefundHandler.HandleRefundByRefundLog(ctx, refundLog, formItem.CashierID)
	util.ResOK(c, "RefundSuccess")

}

// systemPaymentReverse 会员卡支付/赊账支付撤销支付(全额退款)
func (ctrl *PaymentReverseController) systemPaymentReverse(c *gin.Context, merchant *model.MerchantModel, payment *model.MerchantPaymentModel, cashierID int64) {
	ctx := c.Request.Context()

	// 查找退款记录
	refundLog, err := ctrl.RefundLogService.GetByPaymentNo(ctx, merchant.No, payment.PaymentNo, consts.REFUND_TYPE_PAYMENT)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 如果没有则创建
	if refundLog == nil {
		// 记录退款日志(这里是全部退款, 没有订单号/批次号)
		refundLog, err = ctrl.RefundLogService.CreateFromPayment(ctx, merchant, 0, payment, payment.Amount, consts.REFUND_TYPE_PAYMENT)
		if err != nil {
			util.ResError(c, errors.BadRequest("", "RefundFailed"))
			return
		}
	}

	util.ResOK(c, "RefundSuccess")
	if refundLog.RefundStatus == consts.REFUND_STATUS_WAITING || refundLog.RefundStatus == consts.REFUND_STATUS_FAILED {
		go ctrl.RefundHandler.HandleRefundByRefundLog(ctx, refundLog, cashierID)
	}
}
