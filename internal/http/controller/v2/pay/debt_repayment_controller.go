package pay

import (
	"context"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
	"time"

	"github.com/gin-gonic/gin"
)

type DebtRepaymentController struct {
	Trans                     *util.Trans
	MerchantService           *service.MerchantService           // 商户服务
	PaymentService            *service.PaymentService            // 支付服务
	PaymentTypeService        *service.PaymentTypeService        // 支付方式服务
	WechatPayService          *service.WechatPayService          // 微信支付服务
	PaymentLogService         *service.PaymentLogService         // 支付日志服务
	DebtHolderService         *service.DebtHolderService         // 客户服务
	DebtRepaymentOrderService *service.DebtRepaymentOrderService // 还款订单服务
	DebtRepaymentHandler      *handler.DebtRepaymentHandler      // 还款处理器
}

//	线下支付还款
//
// @Tags 赊账还款相关接口
// @Security ApiTokenAuth
// @Summary 线下支付还款 : 现金/个人微信/个人支付宝等线下支付方式都走这个接口
// @Param body body request.DebtRepaymentOfflineForm true "body"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/repayment/offline-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/14 16:49"]
// @X-Version ["2.0"]
func (ctrl *DebtRepaymentController) OfflinePay(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.DebtRepaymentOfflineForm
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}

	// 设置MerchantNo
	formItem.MerchantNo = util.GetMerchantNo(c)
	formItem.CashierID = util.FromUserID(c.Request.Context())
	formItem.DebtRepaymentRequest.PaymentTypeID = formItem.PaymentTypeID

	paymentType, err := ctrl.PaymentTypeService.GetMerchantActivePaymentTypeByID(ctx, formItem.MerchantNo, formItem.PaymentTypeID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if paymentType == nil {
		util.ResError(c, errors.BadRequest("", "PaymentTypeNotFound"))
		return
	}
	if paymentType.Online == 1 {
		util.ResError(c, errors.BadRequest("", "NotSupportOfflinePayment"))
		return
	}

	// 获取客户信息
	holder, err := ctrl.DebtHolderService.GetByID(ctx, formItem.MerchantNo, formItem.HolderID)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if holder == nil {
		util.ResError(c, errors.BadRequest("1001", "DebtHolderNotFound"))
		return
	}

	// 创建还款订单
	repaymentOrder := &model.DebtRepaymentOrderModel{
		No:            util.SerialNumber(consts.DebtRepaymentPrefix),
		MerchantNo:    formItem.MerchantNo,
		HolderID:      formItem.HolderID,
		Amount:        formItem.Amount, // 负数表示还款
		PaymentTypeID: formItem.PaymentTypeID,
		Status:        model.DebtRepaymentOrderStatusSuccess,
		CashierID:     formItem.CashierID,
	}

	err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {

		if err = ctrl.DebtRepaymentOrderService.Create(ctx, repaymentOrder); err != nil {
			return err
		}
		if err = ctrl.DebtRepaymentHandler.UpdateDebtHolderBalance(ctx, holder, repaymentOrder, formItem.PaymentTypeID); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 支付类型
	payType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, formItem.PaymentTypeID)
	if err != nil {
		util.ResError(c, err)
	}
	// 重新获取客户信息
	holder, err = ctrl.DebtHolderService.GetByID(ctx, formItem.MerchantNo, formItem.HolderID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	holderName := holder.NameUg
	paymentTypeName := payType.NameUg
	if i18n.IsZh(&ctx) {
		paymentTypeName = payType.NameZh
		holderName = holder.NameZh
	}
	repaymentResult := schema.DebtRepaymentResult{
		Status:      1, // 1 还款成功
		Mobile:      holder.Phone,
		Name:        holderName,
		Balance:     util.DivideFloat(float64(holder.Balance), 100),
		Amount:      util.DivideFloat(float64(repaymentOrder.Amount), 100),
		PaymentType: paymentTypeName,
	}
	util.ResSuccess(c, repaymentResult, "PaySuccess")
}

// MicroPay 微信付款码还款
//
// @Tags 赊账还款相关接口
// @Security ApiTokenAuth
// @Summary 微信付款码还款
// @Param body body request.DebtRepaymentMicroPayForm true "body"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/repayment/micro-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/14 16:05"]
// @X-Version ["2.0"]
func (ctrl *DebtRepaymentController) MicroPay(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.DebtRepaymentMicroPayForm
	err := util.ParseJSON(c, &formItem)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取MerchantNO
	formItem.MerchantNo = util.GetMerchantNo(c)
	formItem.CashierID = util.FromUserID(ctx)

	// 获取支付方式
	if formItem.PaymentTypeID, err = ctrl.PaymentService.GetPayTypeByAuthCode(formItem.AuthCode); err != nil {
		util.ResError(c, err)
		return
	}

	// 获取客户信息
	holder, merchant, err := ctrl.getDebtRepaymentInfo(ctx, &formItem.DebtRepaymentRequest)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 创建还款订单
	repaymentOrder, paymentLog, err := ctrl.createDebtRepaymentPaymentLog(c, &formItem.DebtRepaymentRequest, consts.TRADE_TYPE_MICROPAY, merchant)
	if err != nil {
		util.ResError(c, err)
		return
	}

	var paymentResult *schema.PaymentResult
	// 根据类型调用方相应的支付接口
	switch formItem.PaymentTypeID {
	case consts.PAY_TYPE_WECHAT:
		paymentResult, err = ctrl.WechatPayService.MicroPay(ctx, formItem.AuthCode, paymentLog, merchant)
		if err != nil {
			util.ResError(c, err)
			return
		}
	case consts.PAY_TYPE_ALIPAY:
		util.ResError(c, errors.BadRequest("", "InvalidPayType"))
		return
	default:
		util.ResError(c, errors.BadRequest("", "InvalidPayType"))
		return
	}
	// 如果支付成功，更新支付信息
	if paymentResult.Status == consts.PAY_STATUS_PAID {
		if err = ctrl.DebtRepaymentHandler.HandleOnlinePaySuccess(ctx, holder, repaymentOrder, paymentResult); err != nil {
			util.ResError(c, err)
			return
		}
		// 支付类型
		payType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, formItem.PaymentTypeID)
		if err != nil {
			util.ResError(c, err)
		}
		// 重新获取客户信息
		holder, err = ctrl.DebtHolderService.GetByID(ctx, merchant.No, repaymentOrder.HolderID)
		if err != nil {
			util.ResError(c, err)
			return
		}
		paymentTypeName := payType.NameUg
		holderName := holder.NameUg
		if i18n.IsZh(&ctx) {
			paymentTypeName = payType.NameZh
			holderName = holder.NameZh
		}
		repaymentResult := schema.DebtRepaymentResult{
			Status:      1, // 1 还款成功
			Mobile:      holder.Phone,
			Name:        holderName,
			Balance:     util.DivideFloat(float64(holder.Balance-repaymentOrder.Amount), 100),
			Amount:      util.DivideFloat(float64(repaymentOrder.Amount), 100),
			PaymentType: paymentTypeName,
		}
		util.ResSuccess(c, repaymentResult, "PaySuccess")
		return
	}
	util.ResError(c, errors.BadRequest("", "PaymentFailed"))
}

// NativePay 微信扫码支付还款
//
// @Tags 赊账还款相关接口
// @Security ApiTokenAuth
// @Summary 微信扫码支付还款
// @Param body body request.DebtRepaymentRequest true "body"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/repayment/native-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/14 16:05"]
// @X-Version ["2.0"]
func (ctrl *DebtRepaymentController) NativePay(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.DebtRepaymentRequest
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	// 设置MerchantNo
	formItem.MerchantNo = util.GetMerchantNo(c)
	formItem.CashierID = util.FromUserID(ctx)
	formItem.PaymentTypeID = consts.PAY_TYPE_WECHAT

	// 获取客户信息
	_, merchant, err := ctrl.getDebtRepaymentInfo(ctx, &formItem)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 创建还款订单
	_, paymentLog, err := ctrl.createDebtRepaymentPaymentLog(c, &formItem, consts.TRADE_TYPE_NATIVE, merchant)
	if err != nil {
		util.ResError(c, err)
		return
	}

	payUrl, err := ctrl.WechatPayService.NativePay(ctx, paymentLog, merchant)
	if err != nil {
		util.ResError(c, err)
		return
	}
	res := map[string]string{
		"payment_no": paymentLog.PaymentNo,
		"pay_url":    payUrl,
	}
	util.ResSuccess(c, res, "GetSuccess")

}

// PaymentQuery 支付结果查询
//
// @Tags 赊账还款相关接口
// @Security ApiTokenAuth
// @Summary 支付结果查询
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/debt/repayment/query/{paymentNo} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/05/14 17:52"]
// @X-Version ["2.0"]
func (ctrl *DebtRepaymentController) PaymentQuery(c *gin.Context) {
	ctx := c.Request.Context()
	paymentNo := c.Param("paymentNo")
	// 获取MerchantNO
	merchantNo := util.GetMerchantNo(c)

	order, err := ctrl.DebtRepaymentOrderService.GetByOrderNo(ctx, merchantNo, paymentNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if order == nil {
		util.ResError(c, errors.BadRequest("", "OrderNotFound"))
		return
	}

	repaymentResult := schema.DebtRepaymentResult{}

	if order.Status == model.DebtRepaymentOrderStatusSuccess {

		payType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, order.PaymentTypeID)
		if err != nil {
			util.ResError(c, err)
			return
		}
		holderName := order.Holder.NameUg
		paymentTypeName := payType.NameUg
		if i18n.IsZh(&ctx) {
			holderName = order.Holder.NameZh
			paymentTypeName = payType.NameZh
		}

		repaymentResult.Status = 1
		repaymentResult.Balance = util.DivideFloat(float64(order.Holder.Balance), 100)
		repaymentResult.Amount = util.DivideFloat(float64(order.Amount), 100)
		repaymentResult.Name = holderName
		repaymentResult.Mobile = order.Holder.Phone
		repaymentResult.PaymentType = paymentTypeName
	}

	util.ResSuccess(c, repaymentResult, "PaymentQuerySuccess")
}

// 获取还款者信息
func (ctrl *DebtRepaymentController) getDebtRepaymentInfo(ctx context.Context, formItem *request.DebtRepaymentRequest) (*model.DebtHolderModel, *model.MerchantModel, error) {

	// 获取商户信息
	var merchant *model.MerchantModel
	var err error
	if merchant, err = ctrl.MerchantService.GetByMerchantNo(ctx, formItem.MerchantNo); err != nil {
		return nil, nil, err
	}

	if merchant == nil {
		return nil, nil, errors.BadRequest("", "MerchantNotFound")
	}
	// 如果未开通微信特约商户
	if merchant.SubMchID == "" {
		return nil, nil, errors.BadRequest("", "MerchantNotSupportWechatPay")
	}

	if merchant.State != model.MerchantStateActive {
		return nil, nil, errors.BadRequest("", "MerchantNotActive")
	}

	// 获取客户信息
	var holder *model.DebtHolderModel
	if holder, err = ctrl.DebtHolderService.GetByID(ctx, formItem.MerchantNo, formItem.HolderID); err != nil {
		return nil, nil, err
	}

	if holder.MerchantNo != formItem.MerchantNo {
		return nil, nil, errors.BadRequest("1002", "DebtHolderNotFound")
	}
	return holder, merchant, nil
}

// 创建还款订单
func (ctrl *DebtRepaymentController) createDebtRepaymentPaymentLog(
	c *gin.Context,
	formItem *request.DebtRepaymentRequest,
	tradeType string,
	merchant *model.MerchantModel) (*model.DebtRepaymentOrderModel, *model.MerchantPaymentLogModel, error) {
	ctx := c.Request.Context()
	// 创建客户还款订单
	repaymentOrder := &model.DebtRepaymentOrderModel{
		No:            util.SerialNumber(consts.DebtRepaymentPrefix),
		MerchantNo:    formItem.MerchantNo,
		HolderID:      formItem.HolderID,
		Amount:        formItem.Amount,
		PaymentTypeID: formItem.PaymentTypeID,
		Status:        model.DebtRepaymentOrderStatusWaiting,
		CashierID:     formItem.CashierID,
	}
	// 构造支付记录
	expiresAt := time.Now().Add(time.Duration(config.C.PaymentConfig.PaymentExpires) * time.Second)
	paymentInfo := &model.MerchantPaymentModel{
		MerchantNo:    repaymentOrder.MerchantNo,
		OrderNo:       repaymentOrder.No,
		PaymentTypeID: formItem.PaymentTypeID,
		CustomerID:    repaymentOrder.HolderID,
		PaymentNo:     repaymentOrder.No,
		Amount:        repaymentOrder.Amount,
		ExpiresAt:     &expiresAt,
	}

	var paymentLog *model.MerchantPaymentLogModel
	var err error

	err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {
		// 保存还款订单

		if err = ctrl.DebtRepaymentOrderService.Create(ctx, repaymentOrder); err != nil {
			return err
		}
		// 创建支付记录
		if paymentLog, err = ctrl.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, tradeType, consts.ORDER_TYPE_DEBT_REPAYMENT, c.ClientIP()); err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, nil, err
	}

	return repaymentOrder, paymentLog, nil
}
