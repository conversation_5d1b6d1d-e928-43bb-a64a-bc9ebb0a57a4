package pay

import (
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

// PaymentController 支付相关接口
type PaymentController struct {
	MerchantService   *service.MerchantService   // 商户服务
	PaymentService    *service.PaymentService    // 支付服务
	WechatPayService  *service.WechatPayService  // 微信支付服务
	AlipayService     *service.AlipayService     // 支付宝支付服务
	PaymentLogService *service.PaymentLogService // 支付日志服务
}

// MicroPay 付款码支付接口
//
// @Tags 支付相关接口
// @Security ServerTokenAuth
// @Summary 付款码支付接口
// @Success 200 {object} util.ResponseResult{data=schema.PaymentResult}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/micro-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/5 16:32"]
// @X-Version ["2.0"]
func (ctrl *PaymentController) MicroPay(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.MicroPayForm
	err := util.ParseJSON(c, &formItem)
	if err != nil {
		util.ResError(c, err)
		return
	}
	// 获取MerchantNO
	formItem.MerchantNo = util.FromServerMerchantNo(ctx)

	// 获取商户信息
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, formItem.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 如果未开通微信特约商户
	if merchant.SubMchID == "" {
		util.ResError(c, errors.BadRequest("", "MerchantNotSupportWechatPay"))
		return
	}

	// 获取支付方式
	formItem.PaymentTypeID, err = ctrl.PaymentService.GetPayTypeByAuthCode(formItem.AuthCode)

	if err != nil {
		util.ResError(c, err)
		return
	}

	// 创建支付信息
	paymentInfo, err := ctrl.PaymentService.CreateMerchantPayment(ctx, &formItem.MerchantPaymentForm)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 创建支付记录
	paymentLog, err := ctrl.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, consts.TRADE_TYPE_MICROPAY, consts.ORDER_TYPE_ORDER, c.ClientIP())
	if err != nil {
		util.ResError(c, err)
		return
	}
	var paymentResult *schema.PaymentResult
	// 根据类型调用方相应的支付接口
	switch formItem.PaymentTypeID {
	case consts.PAY_TYPE_WECHAT:
		paymentResult, err = ctrl.WechatPayService.MicroPay(ctx, formItem.AuthCode, paymentLog, merchant)
		if err != nil {
			util.ResError(c, err)
			return
		}
	case consts.PAY_TYPE_ALIPAY:
		paymentResult, err = ctrl.AlipayService.MicroPay(ctx, formItem.AuthCode, paymentLog, merchant)
		if err != nil {
			util.ResError(c, err)
			return
		}
	default:
		util.ResError(c, errors.BadRequest("", "InvalidPayType"))
		return
	}
	// 如果支付成功，更新支付信息
	if paymentResult.Status == consts.PAY_STATUS_PAID {
		// 更新支付信息
		_, err = ctrl.PaymentLogService.UpdatePaymentStatus(ctx, paymentResult)
		_, err = ctrl.PaymentService.UpdatePaymentStatus(ctx, paymentResult)
		if err != nil {
			util.ResError(c, err)
			return
		}
		util.ResSuccess(c, paymentResult, "PaySuccess")
		return
	}
	util.ResError(c, errors.BadRequest("", "PaymentFailed"))
}

// PaymentQrcodeUrl 获取统一支付二维码
//
// @Tags 支付相关接口
// @Security ServerTokenAuth
// @Summary 获取统一支付二维码
// @Success 200 {object} util.ResponseResult{data=string}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/qrcode [post]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/9 16:33"]
// @X-Version ["2.0"]
func (ctrl *PaymentController) PaymentQrcodeUrl(c *gin.Context) {
	ctx := c.Request.Context()
	var paymentForm request.MerchantPaymentForm
	if err := util.ParseJSON(c, &paymentForm); err != nil {
		util.ResError(c, err)
		return
	}
	// 设置MerchantNo
	paymentForm.MerchantNo = util.FromServerMerchantNo(ctx)

	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, paymentForm.MerchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 如果未开通微信特约商户
	if merchant.SubMchID == "" {
		util.ResError(c, errors.BadRequest("", "MerchantNotSupportWechatPay"))
		return
	}

	// 判断商户状态
	if merchant.State != model.MerchantStateActive {
		util.ResError(c, errors.BadRequest("", "MerchantNotActive"))
		return
	}

	// 如果没有支付宝授权码，走微信支付
	if merchant.AlipayAppAuthToken == "" && merchant.SubMchID != "" {
		paymentForm.PaymentTypeID = consts.PAY_TYPE_WECHAT
		ctrl.wechatNativePay(c, &paymentForm, merchant)
		return
	}

	// 生成二维码内容
	payUrl, err := ctrl.PaymentService.GetPaymentQrcodeUrl(ctx, &paymentForm)
	if err != nil {
		util.ResError(c, err)
		return
	}

	util.ResSuccess(c, payUrl, "GetSuccess")

}

// wechatNativePay 微信扫码支付
func (ctrl *PaymentController) wechatNativePay(
	c *gin.Context,
	paymentForm *request.MerchantPaymentForm,
	merchant *model.MerchantModel) {
	ctx := c.Request.Context()
	// 创建支付信息
	paymentForm.PaymentTypeID = consts.PAY_TYPE_WECHAT
	paymentInfo, err := ctrl.PaymentService.CreateMerchantPayment(ctx, paymentForm)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 创建支付记录
	paymentLog, err := ctrl.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, consts.TRADE_TYPE_NATIVE, consts.ORDER_TYPE_ORDER, c.ClientIP())
	if err != nil {
		util.ResError(c, err)
		return
	}
	payUrl, err := ctrl.WechatPayService.NativePay(ctx, paymentLog, merchant)
	if err != nil {
		util.ResError(c, err)
		return
	}
	util.ResSuccess(c, payUrl, "GetSuccess")
}

// PaymentQuery 支付结果查询接口
//
// @Tags 支付相关接口
// @Security ServerTokenAuth
// @Summary
// @Success 200 {object} util.ResponseResult{data=schema.PaymentResult}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/payment/query/{paymentNo} [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/11/27 16:34"]
// @X-Version ["2.0"]
func (ctrl *PaymentController) PaymentQuery(c *gin.Context) {
	ctx := c.Request.Context()
	paymentNo := c.Param("paymentNo")
	// 获取MerchantNO
	merchantNo := util.FromServerMerchantNo(ctx)

	// 获取支付记录
	paymentLog, err := ctrl.PaymentLogService.GetByPaymentNo(ctx, paymentNo, merchantNo)

	if err != nil {
		util.ResError(c, err)
		return
	} else if paymentLog == nil {
		util.ResSuccess(c, schema.PaymentResult{
			MerchantNo: merchantNo,
			PaymentNo:  paymentNo,
		})
		return
	}
	// 判断商户号是否一致
	if merchantNo != paymentLog.MerchantNo {
		util.ResError(c, errors.BadRequest("", "DataNotFound"))
		return
	}

	// 支付结果
	paymentResult := schema.PaymentResult{
		MerchantNo:    paymentLog.MerchantNo,
		OrderNo:       paymentLog.OrderNo,
		PaymentTypeID: paymentLog.PaymentTypeID,
		PaymentNo:     paymentLog.PaymentNo,
	}

	// 如果已经支付成功，直接返回支付结果
	if paymentLog.Status == consts.PAY_STATUS_PAID {
		paymentResult.Status = paymentLog.Status
		paymentResult.PaidAt = paymentLog.PaidAt
	}
	util.ResSuccess(c, paymentResult, "PaymentQuerySuccess")
}
