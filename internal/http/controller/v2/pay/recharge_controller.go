package pay

import (
	"context"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/config"
	"ros-api-go/internal/consts"
	"ros-api-go/internal/handler"
	"ros-api-go/internal/http/request"
	"ros-api-go/internal/model"
	"ros-api-go/internal/service"
	"ros-api-go/internal/sms"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/logging"
	"ros-api-go/pkg/util"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"go.uber.org/zap"
)

type RechargeController struct {
	Trans                *util.Trans
	MerchantService      *service.MerchantService      // 商户服务
	PaymentService       *service.PaymentService       // 支付服务
	PaymentTypeService   *service.PaymentTypeService   // 支付方式服务
	WechatPayService     *service.WechatPayService     // 微信支付服务
	PaymentLogService    *service.PaymentLogService    // 支付日志服务
	CustomerService      *service.CustomerService      // 客户服务
	RechargeOrderService *service.RechargeOrderService // 充值订单服务
	RechargeHandler      *handler.RechargeHandler      // 充值处理器
}

//	线下支付充值
//
// @Tags VIP充值相关接口
// @Security ApiTokenAuth
// @Summary 线下支付充值 : 现金/个人微信/个人支付宝等线下支付方式都走这个接口
// @Param body body request.RechargeOfflineForm true "body"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/recharge/offline-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/17 16:49"]
// @X-Version ["2.0"]
func (ctrl *RechargeController) OfflinePay(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.RechargeOfflineForm
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}

	// 设置MerchantNo
	formItem.MerchantNo = c.Request.Header.Get("Merchantno")
	formItem.CashierID = util.FromUserID(c.Request.Context())
	formItem.RechargeRequest.PaymentTypeID = formItem.PaymentTypeID

	paymentType, err := ctrl.PaymentTypeService.GetMerchantActivePaymentTypeByID(ctx, formItem.MerchantNo, formItem.PaymentTypeID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if paymentType == nil {
		util.ResError(c, errors.BadRequest("", "PaymentTypeNotFound"))
		return
	}
	if paymentType.Online == 1 {
		util.ResError(c, errors.BadRequest("", "NotSupportOfflinePayment"))
		return
	}

	// 获取客户信息
	customer, err := ctrl.CustomerService.GetByID(ctx, formItem.CustomerID)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if customer == nil || customer.MerchantNo != formItem.MerchantNo {
		util.ResError(c, errors.BadRequest("", "CustomerNotFound"))
		return
	}
	merchant, err := ctrl.MerchantService.GetByMerchantNo(ctx, formItem.MerchantNo)
	if err != nil {
		util.ResError(c, errors.BadRequest("", "MerchantNotFound"))
		return
	}
	// 创建充值订单
	rechargeOrder := &model.CustomerRechargeOrderModel{
		No:             util.SerialNumber(consts.VIPPaymentPrefix),
		MerchantNo:     formItem.MerchantNo,
		CustomerID:     formItem.CustomerID,
		RechargeAmount: formItem.RechargeAmount,
		PresentAmount:  formItem.PresentAmount,
		PaymentTypeID:  formItem.PaymentTypeID,
		Status:         model.RechargeOrderStatusSuccess,
		Type:           model.RechargeOrderTypeCashier,
		CashierID:      formItem.CashierID,
	}

	err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {

		if err = ctrl.RechargeOrderService.Create(ctx, rechargeOrder); err != nil {
			return err
		}
		if err = ctrl.RechargeHandler.UpdateCustomerBalance(ctx, customer, rechargeOrder, formItem.PaymentTypeID); err != nil {
			return err
		}

		return nil
	})
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 支付类型
	payType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, formItem.PaymentTypeID)
	if err != nil {
		util.ResError(c, err)
	}
	// 重新获取客户信息
	customer, err = ctrl.CustomerService.GetByID(ctx, rechargeOrder.CustomerID)
	if err != nil {
		util.ResError(c, err)
		return
	}
	paymentTypeName := payType.NameUg
	if i18n.IsZh(&ctx) {
		paymentTypeName = payType.NameZh
	}
	rechargeResult := schema.RechargeResult{
		Status:         1, // 1 充值成功
		Mobile:         customer.Mobile,
		Name:           customer.Name,
		Balance:        customer.Balance,
		RechargeAmount: util.DivideFloat(float64(rechargeOrder.RechargeAmount), 100),
		PresentAmount:  util.DivideFloat(float64(rechargeOrder.PresentAmount), 100),
		PaymentType:    paymentTypeName,
	}
	util.ResSuccess(c, rechargeResult, "PaySuccess")
	if merchant.SMSCount > 0 {
		price := util.DivideFloat(float64(rechargeOrder.RechargeAmount+rechargeOrder.PresentAmount), 100)
		leftPrice := customer.Balance

		go func() {
			newCtx := context.Background()
			defer func() {
				if r := recover(); r != nil {
					logging.Context(newCtx).Error("sms_fail 会员消费发送消息失败（panic）",
						zap.String("mobile", customer.Mobile),
						zap.Int64("customer_id", customer.ID),
						zap.String("merchant_no", customer.MerchantNo),
						zap.Int64("recharge_id", rechargeOrder.ID),
						zap.String("price", strconv.FormatFloat(price, 'f', 2, 64)),
						zap.String("leftPrice", strconv.FormatFloat(leftPrice, 'f', 2, 64)),
						zap.Any("error", r))
				}
			}()
			err := sms.SendCustomerRechargeSMS(
				customer.Mobile,
				merchant.NameZh,
				strconv.FormatFloat(price, 'f', 2, 64),
				strconv.FormatFloat(leftPrice, 'f', 2, 64),
			)
			if err != nil {
				logging.Context(newCtx).Error("sms_fail 会员消费发送消息失败",
					zap.String("mobile", customer.Mobile),
					zap.Int64("customer_id", customer.ID),
					zap.String("merchant_no", customer.MerchantNo),
					zap.Int64("recharge_id", rechargeOrder.ID),
					zap.String("price", strconv.FormatFloat(price, 'f', 2, 64)),
					zap.String("leftPrice", strconv.FormatFloat(leftPrice, 'f', 2, 64)),
					zap.Error(err))
			}
			ctrl.MerchantService.DecreaseSMSCount(newCtx, merchant.No)
		}()
	}
}

// MicroPay 微信付款码充值
//
// @Tags VIP充值相关接口
// @Security ApiTokenAuth
// @Summary 微信付款码充值
// @Param body body request.RechargeMicroPayForm true "body"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/recharge/micro-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/17 16:05"]
// @X-Version ["2.0"]
func (ctrl *RechargeController) MicroPay(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.RechargeMicroPayForm
	err := util.ParseJSON(c, &formItem)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 获取MerchantNO
	formItem.MerchantNo = c.Request.Header.Get("Merchantno")
	formItem.CashierID = util.FromUserID(ctx)

	// 获取支付方式
	if formItem.PaymentTypeID, err = ctrl.PaymentService.GetPayTypeByAuthCode(formItem.AuthCode); err != nil {
		util.ResError(c, err)
		return
	}

	// 获取客户信息
	customer, merchant, err := ctrl.getRechargerInfo(ctx, &formItem.RechargeRequest)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 创建充值订单
	rechargeOrder, paymentLog, err := ctrl.createRechargePaymentLog(c, &formItem.RechargeRequest, consts.TRADE_TYPE_MICROPAY, merchant)
	if err != nil {
		util.ResError(c, err)
		return
	}

	var paymentResult *schema.PaymentResult
	// 根据类型调用方相应的支付接口
	switch formItem.PaymentTypeID {
	case consts.PAY_TYPE_WECHAT:
		paymentResult, err = ctrl.WechatPayService.MicroPay(ctx, formItem.AuthCode, paymentLog, merchant)
		if err != nil {
			util.ResError(c, err)
			return
		}
	case consts.PAY_TYPE_ALIPAY:
		util.ResError(c, errors.BadRequest("", "InvalidPayType"))
		return
	default:
		util.ResError(c, errors.BadRequest("", "InvalidPayType"))
		return
	}
	// 如果支付成功，更新支付信息
	if paymentResult.Status == consts.PAY_STATUS_PAID {
		if err = ctrl.RechargeHandler.HandleOnlinePaySuccess(ctx, customer, rechargeOrder, paymentResult); err != nil {
			util.ResError(c, err)
			return
		}
		// 支付类型
		payType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, formItem.PaymentTypeID)
		if err != nil {
			util.ResError(c, err)
		}
		// 重新获取客户信息
		customer, err = ctrl.CustomerService.GetByID(ctx, rechargeOrder.CustomerID)
		if err != nil {
			util.ResError(c, err)
			return
		}
		paymentTypeName := payType.NameUg
		if i18n.IsZh(&ctx) {
			paymentTypeName = payType.NameZh
		}
		rechargeResult := schema.RechargeResult{
			Status:         1, // 1 充值成功
			Mobile:         customer.Mobile,
			Name:           customer.Name,
			Balance:        customer.Balance,
			RechargeAmount: util.DivideFloat(float64(rechargeOrder.RechargeAmount), 100),
			PresentAmount:  util.DivideFloat(float64(rechargeOrder.PresentAmount), 100),
			PaymentType:    paymentTypeName,
		}
		util.ResSuccess(c, rechargeResult, "PaySuccess")
		return
	}
	util.ResError(c, errors.BadRequest("", "PaymentFailed"))
}

// @Summary 微信扫码支付充值
//
// @Tags 同步接口
// @Security ApiTokenAuth
// @Summary 微信扫码支付充值
// @Param body body request.RechargeRequest true "body"
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/recharge/native-pay [post]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/17 16:05"]
// @X-Version ["2.0"]
func (ctrl *RechargeController) NativePay(c *gin.Context) {
	ctx := c.Request.Context()
	var formItem request.RechargeRequest
	if err := util.ParseJSON(c, &formItem); err != nil {
		util.ResError(c, err)
		return
	}
	// 设置MerchantNo
	formItem.MerchantNo = c.Request.Header.Get("Merchantno")
	formItem.CashierID = util.FromUserID(ctx)
	formItem.PaymentTypeID = consts.PAY_TYPE_WECHAT

	// 获取客户信息
	_, merchant, err := ctrl.getRechargerInfo(ctx, &formItem)
	if err != nil {
		util.ResError(c, err)
		return
	}

	// 创建充值订单
	_, paymentLog, err := ctrl.createRechargePaymentLog(c, &formItem, consts.TRADE_TYPE_NATIVE, merchant)
	if err != nil {
		util.ResError(c, err)
		return
	}

	payUrl, err := ctrl.WechatPayService.NativePay(ctx, paymentLog, merchant)
	if err != nil {
		util.ResError(c, err)
		return
	}
	res := map[string]string{
		"payment_no": paymentLog.PaymentNo,
		"pay_url":    payUrl,
	}
	util.ResSuccess(c, res, "GetSuccess")

}

// 支付结果查询
//
// @Tags VIP充值相关接口
// @Security ApiTokenAuth
// @Summary 支付结果查询
// @Success 200 {object} util.ResponseResult{}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/recharge/query/{paymentNo} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/1/17 17:52"]
// @X-Version ["2.0"]
func (ctrl *RechargeController) PaymentQuery(c *gin.Context) {
	ctx := c.Request.Context()
	paymentNo := c.Param("paymentNo")
	// 获取MerchantNO
	merchantNo := c.Request.Header.Get("Merchantno")

	order, err := ctrl.RechargeOrderService.GetByOrderNo(ctx, merchantNo, paymentNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	if order == nil {
		util.ResError(c, errors.BadRequest("", "OrderNotFound"))
		return
	}

	rechargeResult := schema.RechargeResult{}

	if order.Status == model.RechargeOrderStatusSuccess {

		payType, err := ctrl.PaymentTypeService.GetPaymentTypeByID(ctx, order.PaymentTypeID)
		if err != nil {
			util.ResError(c, err)
			return
		}
		paymentTypeName := payType.NameUg
		if i18n.IsZh(&ctx) {
			paymentTypeName = payType.NameZh
		}

		rechargeResult.Status = 1
		rechargeResult.Balance = order.Customer.Balance
		rechargeResult.RechargeAmount = util.DivideFloat(float64(order.RechargeAmount), 100)
		rechargeResult.PresentAmount = util.DivideFloat(float64(order.PresentAmount), 100)
		rechargeResult.Name = order.Customer.Name
		rechargeResult.Mobile = order.Customer.Mobile
		rechargeResult.PaymentType = paymentTypeName
	}

	util.ResSuccess(c, rechargeResult, "PaymentQuerySuccess")
}

// 获取充值者信息
func (ctrl *RechargeController) getRechargerInfo(ctx context.Context, formItem *request.RechargeRequest) (*model.CustomerModel, *model.MerchantModel, error) {

	// 获取商户信息
	var merchant *model.MerchantModel
	var err error
	if merchant, err = ctrl.MerchantService.GetByMerchantNo(ctx, formItem.MerchantNo); err != nil {
		return nil, nil, err
	}

	if merchant == nil {
		return nil, nil, errors.BadRequest("", "MerchantNotFound")
	}
	// 如果未开通微信特约商户
	if merchant.SubMchID == "" {
		return nil, nil, errors.BadRequest("", "MerchantNotSupportWechatPay")
	}

	if merchant.State != model.MerchantStateActive {
		return nil, nil, errors.BadRequest("", "MerchantNotActive")
	}

	// 获取客户信息
	var customer *model.CustomerModel
	if customer, err = ctrl.CustomerService.GetByID(ctx, formItem.CustomerID); err != nil {
		return nil, nil, err
	}

	if customer.MerchantNo != formItem.MerchantNo {
		return nil, nil, errors.BadRequest("", "CustomerNotFound")
	}
	return customer, merchant, nil
}

// 创建充值订单
func (ctrl *RechargeController) createRechargePaymentLog(
	c *gin.Context,
	formItem *request.RechargeRequest,
	tradeType string,
	merchant *model.MerchantModel) (*model.CustomerRechargeOrderModel, *model.MerchantPaymentLogModel, error) {
	ctx := c.Request.Context()
	// 创建客户充值订单
	rechargeOrder := &model.CustomerRechargeOrderModel{
		No:             util.SerialNumber(consts.VIPPaymentPrefix),
		MerchantNo:     formItem.MerchantNo,
		CustomerID:     formItem.CustomerID,
		RechargeAmount: formItem.RechargeAmount,
		PresentAmount:  formItem.PresentAmount,
		PaymentTypeID:  formItem.PaymentTypeID,
		Status:         model.RechargeOrderStatusWaiting,
		Type:           model.RechargeOrderTypeCashier,
		CashierID:      formItem.CashierID,
	}
	// 构造支付记录
	expiresAt := time.Now().Add(time.Duration(config.C.PaymentConfig.PaymentExpires) * time.Second)
	paymentInfo := &model.MerchantPaymentModel{
		MerchantNo:    rechargeOrder.MerchantNo,
		OrderNo:       rechargeOrder.No,
		PaymentTypeID: formItem.PaymentTypeID,
		CustomerID:    rechargeOrder.CustomerID,
		PaymentNo:     rechargeOrder.No,
		Amount:        rechargeOrder.RechargeAmount,
		ExpiresAt:     &expiresAt,
	}

	var paymentLog *model.MerchantPaymentLogModel
	var err error

	err = ctrl.Trans.Exec(ctx, func(ctx context.Context) error {
		// 保存充值订单

		if err = ctrl.RechargeOrderService.Create(ctx, rechargeOrder); err != nil {
			return err
		}
		// 创建支付记录
		if paymentLog, err = ctrl.PaymentLogService.CreatePaymentLog(ctx, merchant, paymentInfo, tradeType, consts.ORDER_TYPE_RECHARGE, c.ClientIP()); err != nil {
			return err
		}
		return nil
	})

	if err != nil {
		return nil, nil, err
	}

	return rechargeOrder, paymentLog, nil
}
