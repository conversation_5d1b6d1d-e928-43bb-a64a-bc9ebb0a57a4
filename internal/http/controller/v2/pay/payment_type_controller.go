package pay

import (
	"ros-api-go/internal/http/resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/util"

	"github.com/gin-gonic/gin"
)

type PaymentTypeController struct {
	PaymentTypeService *service.PaymentTypeService
}

// GetPaymentTypesForRecharge 获取商家用于充值/还款的支付方式列表
//
// @Tags 支付方式
// @Security ApiTokenAuth
// @Summary 同步平台支付方式接口
// @Success 200 {object} util.ResponseResult{data=[]resource.PaymentTypeResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /api/v2/sync/payment-types [get]
// @X-Author ["Merdan"]
// @X-Date ["2024/12/9 17:02"]
// @X-Version ["2.0"]
func (ctrl *PaymentTypeController) GetPaymentTypesForRecharge(c *gin.Context) {
	ctx := c.Request.Context()
	merchantNo := util.GetMerchantNo(c)
	result, err := ctrl.PaymentTypeService.GetPaymentTypesForRecharge(ctx, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}
	typeResource := resource.PaymentTypeResource{}
	util.ResSuccess(c, typeResource.Collection(result))
}
