package v2

import (
	"github.com/gin-gonic/gin"
	"ros-api-go/internal/http/resource/order_resource"
	"ros-api-go/internal/service"
	"ros-api-go/pkg/errors"
	"ros-api-go/pkg/util"
)

type UserController struct {
	OrderService *service.OrderService
}

//	MyOrders 点菜端-我的-我的订单
//
// @Tags 同步接口
// @Security ApiTokenAuth
// @Summary 点菜端-我的-我的订单
// @Success 200 {object} util.ResponseResult{Data=[]order_resource.MyOrderListItemResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /cloud/api/v2/user/my-orders [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/4/15 17:06"]
// @X-Version ["2.0"]
func (ctrl *UserController) MyOrders(c *gin.Context) {

	ctx := c.Request.Context()
	userID := util.FromUserID(ctx)
	merchantNo := c.Request.Header.Get("merchantNo")
	orders, err := ctrl.OrderService.GetUserOrdersToday(ctx, userID, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	resource := order_resource.MyOrderListItemResource{}

	util.ResSuccess(c, resource.Collection(&ctx, orders))
}

//	GetOrder 点菜端-我的-订单详情
//
// @Tags 同步接口
// @Security ApiTokenAuth
// @Summary 点菜端-我的-订单详情
// @Param orderID path int true "订单ID"
// @Success 200 {object} util.ResponseResult{Data=order_resource.MyOrderResource}
// @Failure 401 {object} util.ResponseResult
// @Failure 404 {object} util.ResponseResult
// @Failure 500 {object} util.ResponseResult
// @Router /cloud/api/v2/user/order/{orderID} [get]
// @X-Author ["Merdan"]
// @X-Date ["2025/4/17 11:55"]
// @X-Version ["2.0"]
func (ctrl *UserController) GetOrder(c *gin.Context) {
	ctx := c.Request.Context()
	orderID := util.StrToInt64(c.Param("order_id"))
	userID := util.FromUserID(ctx)
	merchantNo := c.Request.Header.Get("merchantNo")
	order, err := ctrl.OrderService.GetUserOrderDetail(ctx, orderID, userID, merchantNo)
	if err != nil {
		util.ResError(c, err)
		return
	}

	if order == nil {
		util.ResError(c, errors.NotFound("MyOrders", "OrderNotExists"))
		return
	}
	resource := order_resource.MyOrderResource{}
	util.ResSuccess(c, resource.Make(ctx, order))
}
