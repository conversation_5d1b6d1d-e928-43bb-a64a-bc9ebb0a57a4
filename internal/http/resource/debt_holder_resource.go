package resource

import (
	"context"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

type DebtHolderTop struct {
	TotalCount   int64   `json:"total_count"`
	TotalBalance float64 `json:"total_balance"`
}

type DebtHolderListResource struct {
	Message string                 `json:"message"`
	Data    []*DebtHolderResource  `json:"data"`
	Top     DebtHolderTop          `json:"top,omitempty"`
	Meta    *util.PaginationResult `json:"meta"`
}

// DebtHolderResource represents the debt holder resource for API responses
type DebtHolderResource struct {
	ID          int64   `json:"id"`
	MerchantNo  string  `json:"merchant_no"`          // 商户号
	NameZh      string  `json:"name_zh,omitempty"`    // 中文姓名
	NameUg      string  `json:"name_ug,omitempty"`    // 维语姓名
	Phone       string  `json:"phone"`                // 手机号
	CreditLimit int64   `json:"credit_limit"`         // 信用额度(分)
	Balance     float64 `json:"balance"`              // 当前赊账金额
	Status      uint8   `json:"status"`               // 状态：0 禁用 1 启用
	CreatedBy   int64   `json:"created_by"`           // 创建人
	CreatedAt   *string `json:"created_at,omitempty"` // 创建时间
	UpdatedAt   *string `json:"updated_at,omitempty"` // 更新时间

	CashierName string `json:"cashier_name,omitempty"` // 收银员姓名
}

// Make creates a DebtHolderResource from a DebtHolderModel
func (rc *DebtHolderResource) Make(ctx *context.Context, item *model.DebtHolderModel) *DebtHolderResource {
	if item == nil {
		return nil
	}

	data := &DebtHolderResource{
		ID:          item.ID,
		MerchantNo:  item.MerchantNo,
		NameZh:      item.NameZh,
		NameUg:      item.NameUg,
		Phone:       item.Phone,
		CreditLimit: item.CreditLimit,
		Balance:     util.DivideFloat(float64(item.Balance), 100),
		Status:      item.Status,
		CreatedBy:   item.CreatedBy,
		CreatedAt:   util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:   util.FormatDateTime(item.UpdatedAt),
	}

	if item.Cashier != nil {
		isZh := i18n.IsZh(ctx)
		if isZh {
			data.CashierName = item.Cashier.NameZh
		} else {
			data.CashierName = item.Cashier.NameUg
		}
	}
	return data
}

// Collection creates a slice of DebtHolderResource from a slice of DebtHolderModel
func (rc *DebtHolderResource) Collection(ctx *context.Context, items []*model.DebtHolderModel) []*DebtHolderResource {
	if items == nil || len(items) == 0 {
		return []*DebtHolderResource{}
	}

	result := make([]*DebtHolderResource, len(items))
	for i, item := range items {
		result[i] = rc.Make(ctx, item)
	}

	return result
}
