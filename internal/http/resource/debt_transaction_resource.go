package resource

import (
	"context"
	"ros-api-go/internal/common/schema"
	"ros-api-go/internal/model"
	"ros-api-go/pkg/i18n"
	"ros-api-go/pkg/util"
)

// DebtTransactionResource 赊账交易记录资源
type DebtTransactionResource struct {
	ID          int64   `json:"id"`
	MerchantNo  string  `json:"merchant_no"`          // 商家编号
	OrderID     *int64  `json:"order_id,omitempty"`   // 订单ID
	OrderNo     *string `json:"order_no,omitempty"`   // 订单编号
	HolderID    int64   `json:"holder_id"`            // 赊账人ID
	Amount      float64 `json:"amount"`               // 金额
	Balance     float64 `json:"balance"`              // 余额
	Type        uint8   `json:"type"`                 // 交易类型 1 赊账 2 结账
	TypeLabel   string  `json:"type_label"`           // 交易类型名称
	CashierID   int64   `json:"cashier_id"`           // 创建人
	CreatedAt   *string `json:"created_at,omitempty"` // 创建时间
	UpdatedAt   *string `json:"updated_at,omitempty"` // 更新时间
	HolderName  string  `json:"holder_name"`          // 赊账人姓名
	CashierName string  `json:"cashier_name"`         // 创建人姓名
}

// Make 创建资源
func (rc *DebtTransactionResource) Make(ctx context.Context, item *model.DebtTransactionModel) *DebtTransactionResource {
	if item == nil {
		return nil
	}

	resource := &DebtTransactionResource{
		ID:         item.ID,
		MerchantNo: item.MerchantNo,
		OrderID:    item.OrderID,
		OrderNo:    item.OrderNo,
		HolderID:   item.HolderID,
		Amount:     util.DivideFloat(float64(item.Amount), 100),
		Balance:    util.DivideFloat(float64(item.Balance), 100),
		Type:       item.Type,
		TypeLabel:  rc.GetDebtTransactionTypeLabel(&ctx, item.Type),
		CashierID:  item.CashierID,
		CreatedAt:  util.FormatDateTime(&item.CreatedAt),
		UpdatedAt:  util.FormatDateTime(item.UpdatedAt),
	}

	isZh := i18n.IsZh(&ctx)

	// 关联
	if item.Holder != nil {
		resource.HolderName = item.Holder.NameUg
		if isZh {
			resource.HolderName = item.Holder.NameZh
		}
	}

	if item.Cashier != nil {
		resource.CashierName = item.Cashier.NameUg
		if isZh {
			resource.CashierName = item.Cashier.NameZh
		}
	}

	return resource
}

// Collection 创建资源集合
func (rc *DebtTransactionResource) Collection(ctx context.Context, items []*model.DebtTransactionModel) []*DebtTransactionResource {
	if items == nil || len(items) == 0 {
		return []*DebtTransactionResource{}
	}

	result := make([]*DebtTransactionResource, len(items))
	for i, item := range items {
		result[i] = rc.Make(ctx, item)
	}

	return result
}

// GetDebtTransactionTypeLabel 获取赊账交易类型标签
func (rc *DebtTransactionResource) GetDebtTransactionTypeLabel(ctx *context.Context, t uint8) string {
	switch t {
	case 1:
		return i18n.Msg(ctx, "DebtTransactionTypeDebt")
	case 2:
		return i18n.Msg(ctx, "DebtTransactionTypeRepayment")
	default:
		return i18n.Msg(ctx, "DebtTransactionTypeRefund")
	}
}

type DebtTransactionListResource struct {
	Message string                       `json:"message"`
	Data    interface{}                  `json:"data,omitempty"`
	Error   interface{}                  `json:"errors,omitempty"`
	Meta    *util.PaginationResult       `json:"meta,omitempty"`
	Types   []schema.DebtTransactionType `json:"types"`
}
