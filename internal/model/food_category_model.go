package model

import (
	"gorm.io/gorm"
	"time"
)

const (
	FoodCategoryStateDisabled = 0 // 删除
	FoodCategoryStateNormal   = 1 // 正常
)

// FoodCategoryModel 食品分类
type FoodCategoryModel struct {
	ID         int64          `json:"id" gorm:"size:20;primaryKey;"` // Unique ID
	MerchantNo string         `json:"merchant_no" gorm:"size:20;"`   // 商家编号
	NameUg     string         `json:"name_ug" gorm:"size:50;"`       // 名称(维语)
	NameZh     string         `json:"name_zh" gorm:"size:50;"`       // 名称(中文)
	Sort       int64          `json:"sort"`                          // 排序
	State      int64          `json:"state" gorm:"size:10;"`         // 状态
	CreatedAt  time.Time      `gorm:"column:created_at"`             // 创建时间
	UpdatedAt  time.Time      `gorm:"column:updated_at"`             // 更新时间
	DeletedAt  gorm.DeletedAt `gorm:"column:deleted_at"`             // 删除时间

	Foods      []*FoodModel `json:"foods" gorm:"foreignKey:FoodCategoryID"` // 关联的食品
	FoodsCount int          `gorm:"<-:false;column:foods_count"`
}

func (a *FoodCategoryModel) TableName() string {
	return "food_categories"
}
