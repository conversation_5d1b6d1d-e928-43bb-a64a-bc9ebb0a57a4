package model

import (
	"fmt"
	jsoniter "github.com/json-iterator/go"
	"time"
)

const (
	MerchantStateActive = 1
	MerchantInactive    = 0
)

const (
	MerchantModeLocal  = 1
	MerchantModeOnline = 2
)

// MerchantModel 餐厅
type MerchantModel struct {
	ID                   int64      `gorm:"column:id;size:20;primaryKey;autoIncrement"` // 自增编号
	RegionID             int64      `gorm:"column:region_id"`                           // 区域ID
	RegionAreaID         int64      `gorm:"column:region_area_id"`                      // 区域区域ID
	CategoryID           int64      `gorm:"column:category_id"`                         // 餐厅类别ID
	No                   string     `gorm:"column:no"`                                  // 商家唯一编号
	NameUg               string     `gorm:"column:name_ug"`                             // 维文名称
	NameZh               string     `gorm:"column:name_zh"`                             // 中文名称
	FirstNameUg          string     `gorm:"column:first_name_ug"`                       // 维文首字母
	FirstNameZh          string     `gorm:"column:first_name_zh"`                       // 中文首字母
	Phone                string     `gorm:"column:phone"`                               // 电话号码
	AddressUg            string     `gorm:"column:address_ug"`                          // 维文地址
	AddressZh            string     `gorm:"column:address_zh"`                          // 中文地址
	Logo                 string     `gorm:"column:logo"`                                // 餐厅Logo
	BusinessLicense      string     `gorm:"column:business_license"`                    // 营业执照
	SellerID             int64      `gorm:"column:seller_id"`                           // 商家ID
	BusinessCertificate1 string     `gorm:"column:business_certificate_1"`              // 商家证件1
	BusinessCertificate2 string     `gorm:"column:business_certificate_2"`              // 商家证件2
	BusinessCertificate3 string     `gorm:"column:business_certificate_3"`              // 商家证件3
	Lat                  *float64   `gorm:"column:lat"`                                 // 纬度
	Lng                  *float64   `gorm:"column:lng"`                                 // 经度
	Location             string     `gorm:"column:location"`                            // 位置
	ExpiredAt            *time.Time `gorm:"column:expired_at"`                          // 过期时间，时间戳
	DiancaiPay           int        `gorm:"column:diancai_pay"`                         // 点菜端是否可以现金结账，是否支持收银端收现金
	RefundPassword       string     `gorm:"column:refund_password"`                     // 退单密码
	CancelPassword       string     `gorm:"column:cancel_password"`                     // 取消美食密码
	SMSCount             int64      `gorm:"column:sms_count"`                           // 剩余的短信数
	// 支付相关信息
	WechatPaymentType  int64             `gorm:"column:wechat_payment_type"`          // 是否特约商户, 0 普通转账模式、 1 特约商户模式
	SubMerchantNo      string            `gorm:"column:sub_merchant_no"`              // 微信特约商户号
	MchID              string            `gorm:"column:mch_id"`                       // 微信支付商户号
	SubMchID           string            `gorm:"column:sub_mch_id"`                   // 微信支付特约商户号
	AlipaySubMchID     string            `gorm:"column:alipay_sub_mch_id"`            // 支付宝支付特约商户号
	AlipayAppAuthToken string            `gorm:"column:alipay_app_auth_token"`        // 支付宝开放平台授权token
	State              int64             `gorm:"column:state"`                        // 状态，0 正常、 1 停用
	Mode               int               `gorm:"column:mode"`                         // 商家模式：1 本地模式(默认) 2 线上模式
	CreatedAt          *time.Time        `gorm:"column:created_at"`                   // Create time
	UpdatedAt          *time.Time        `gorm:"column:updated_at"`                   // Update time
	LocalServer        *LocalServerModel `gorm:"foreignKey:MerchantNo;references:No"` // 本地服务器信息(获取时Status=1)
}

func (model *MerchantModel) String() string {
	s, err := jsoniter.MarshalToString(model)
	if err != nil {
		fmt.Println("Failed to marshal json string: " + err.Error())
		return ""
	}
	return s
}

func (model *MerchantModel) TableName() string {
	return "merchants"
}

func (model *MerchantModel) IsPartner() bool {
	return model.WechatPaymentType == 1
}
